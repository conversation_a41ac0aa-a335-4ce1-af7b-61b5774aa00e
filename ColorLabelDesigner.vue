<template>
  <div class="color-label-designer">
    <!-- 操作面板 -->
    <div class="control-panel">
      <el-card class="control-card">
        <template #header>
          <div class="card-header">
            <span>彩色标签设计器</span>
            <el-tag type="warning">[支持Chrome浏览器]</el-tag>
          </div>
        </template>

        <!-- 文本输入 -->
        <div class="control-group">
          <el-form-item label="标签文本">
            <el-input
              v-model="labelConfig.labelText"
              placeholder="请输入标签文本"
              @input="handleTextChange"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </div>

        <!-- 颜色选择器 -->
        <div class="control-group">
          <el-form-item label="文字颜色">
            <el-color-picker
              v-model="labelConfig.textColor"
              @change="handleColorChange"
              show-alpha
            />
          </el-form-item>
        </div>

        <!-- 字体设置 -->
        <div class="control-group">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="字体">
                <el-select v-model="labelConfig.fontFamily" @change="handleFontChange">
                  <el-option
                    v-for="font in fontList"
                    :key="font.value"
                    :label="font.name"
                    :value="font.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="字号">
                <el-input-number
                  v-model="labelConfig.fontSize"
                  :min="8"
                  :max="72"
                  @change="handleFontSizeChange"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 字体控制按钮 -->
        <div class="control-group">
          <el-button-group>
            <el-button @click="decreaseFontSize">小</el-button>
            <el-button @click="toggleFontWeight">B</el-button>
            <el-button @click="increaseFontSize">大</el-button>
            <el-button @click="autoAdjustFont">智能</el-button>
          </el-button-group>
        </div>

        <!-- 预设模板 -->
        <div class="control-group">
          <el-form-item label="预设模板">
            <el-select
              v-model="selectedTemplate"
              placeholder="请选择模板"
              @change="handleTemplateChange"
            >
              <el-option label="请选择模板" value="" />
              <el-option
                v-for="template in presetTemplates"
                :key="template.name"
                :label="template.name"
                :value="template.name"
              />
            </el-select>
          </el-form-item>
        </div>

        <!-- 标签数量 -->
        <div class="control-group">
          <el-form-item label="标签数量">
            <el-input-number
              v-model="labelConfig.labelCount"
              :min="1"
              :max="100"
              @change="handleCountChange"
            />
          </el-form-item>
        </div>

        <!-- 操作按钮 -->
        <div class="control-group">
          <el-button-group>
            <el-button type="primary" @click="handlePreview">预览</el-button>
            <el-button type="success" @click="handlePrint">打印</el-button>
            <el-button type="info" @click="handleSaveTemplate">保存模板</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-button-group>
        </div>
      </el-card>

      <!-- 颜色预设面板 -->
      <el-card class="color-presets-card">
        <template #header>
          <span>颜色预设表</span>
        </template>
        <div class="color-presets">
          <div
            v-for="color in colorPresets"
            :key="color.value"
            class="color-preset-item"
            @click="selectPresetColor(color.value)"
          >
            <div
              class="color-preview"
              :style="{ backgroundColor: color.value }"
            ></div>
            <div class="color-info">
              <div class="color-name">{{ color.name }}</div>
              <div class="color-description">{{ color.description }}</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 预览区域 -->
    <div class="preview-panel">
      <el-card class="preview-card">
        <template #header>
          <span>标签预览</span>
        </template>
        <div class="label-preview-container">
          <div
            v-for="(item, index) in previewLabels"
            :key="index"
            class="label-item"
            :style="getLabelStyle()"
            v-html="getLabelContent()"
          ></div>
        </div>
      </el-card>
    </div>

    <!-- 打印预览对话框 -->
    <el-dialog
      v-model="showPrintPreview"
      title="打印预览"
      width="80%"
      :before-close="handleClosePrintPreview"
    >
      <div class="print-preview-content" v-html="printPreviewHtml"></div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showPrintPreview = false">取消</el-button>
          <el-button type="primary" @click="confirmPrint">确认打印</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 保存模板对话框 -->
    <el-dialog
      v-model="showSaveDialog"
      title="保存模板"
      width="400px"
    >
      <el-form>
        <el-form-item label="模板名称">
          <el-input
            v-model="customTemplateName"
            placeholder="请输入模板名称"
            maxlength="20"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showSaveDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmSaveTemplate">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { colorLabelApi } from '@/api/color-label';

// 响应式数据
const labelConfig = reactive({
  labelText: '',
  textColor: '#0000FF',
  backgroundColor: '',
  fontFamily: '青鸟华光简行楷',
  fontSize: 40,
  fontWeight: 'normal',
  labelCount: 10
});

const selectedTemplate = ref('');
const showPrintPreview = ref(false);
const showSaveDialog = ref(false);
const customTemplateName = ref('');
const printPreviewHtml = ref('');

// 数据列表
const fontList = ref([]);
const colorPresets = ref([]);
const presetTemplates = ref([]);

// 计算属性
const previewLabels = computed(() => {
  return Array.from({ length: Math.min(labelConfig.labelCount, 10) }, (_, i) => i);
});

// 生命周期
onMounted(async () => {
  await loadInitialData();
});

// 方法
const loadInitialData = async () => {
  try {
    // 加载字体列表
    const fonts = await colorLabelApi.getFontList();
    fontList.value = fonts;

    // 加载颜色预设
    const colors = await colorLabelApi.getColorPresets();
    colorPresets.value = colors;

    // 加载预设模板
    const templates = await colorLabelApi.getPresetTemplates();
    presetTemplates.value = templates;
  } catch (error) {
    ElMessage.error('加载数据失败：' + error.message);
  }
};

const handleTextChange = () => {
  // 根据文字长度智能调整字体大小
  const textLength = labelConfig.labelText.length;
  if (textLength <= 4) {
    labelConfig.fontSize = 40;
  } else if (textLength === 5) {
    labelConfig.fontSize = 32;
  } else if (textLength >= 6) {
    labelConfig.fontSize = 27;
  }
};

const handleColorChange = (color) => {
  labelConfig.textColor = color;
};

const handleFontChange = () => {
  // 字体变化时的处理
};

const handleFontSizeChange = () => {
  // 字体大小变化时的处理
};

const handleCountChange = () => {
  // 标签数量变化时的处理
};

const decreaseFontSize = () => {
  if (labelConfig.fontSize > 8) {
    labelConfig.fontSize--;
  }
};

const increaseFontSize = () => {
  if (labelConfig.fontSize < 72) {
    labelConfig.fontSize++;
  }
};

const toggleFontWeight = () => {
  labelConfig.fontWeight = labelConfig.fontWeight === 'bold' ? 'normal' : 'bold';
};

const autoAdjustFont = () => {
  handleTextChange();
};

const handleTemplateChange = (templateName) => {
  const template = presetTemplates.value.find(t => t.name === templateName);
  if (template) {
    labelConfig.textColor = template.color;
    labelConfig.fontFamily = template.fontFamily;
    labelConfig.labelText = template.content.replace(/<[^>]*>/g, ''); // 移除HTML标签
  }
};

const selectPresetColor = (color) => {
  labelConfig.textColor = color;
};

const getLabelStyle = () => {
  return {
    color: labelConfig.textColor,
    fontFamily: labelConfig.fontFamily,
    fontSize: labelConfig.fontSize + 'px',
    fontWeight: labelConfig.fontWeight,
    backgroundColor: labelConfig.backgroundColor || 'transparent'
  };
};

const getLabelContent = () => {
  return labelConfig.labelText;
};

const handlePreview = async () => {
  try {
    const preview = await colorLabelApi.generatePreview(labelConfig);
    printPreviewHtml.value = preview.previewHtml;
    showPrintPreview.value = true;
  } catch (error) {
    ElMessage.error('生成预览失败：' + error.message);
  }
};

const handlePrint = async () => {
  try {
    const printData = await colorLabelApi.batchGenerate(labelConfig);
    
    // 创建打印窗口
    const printWindow = window.open('', '_blank');
    printWindow.document.write(printData.html);
    printWindow.document.close();
    
    // 等待内容加载完成后打印
    printWindow.onload = () => {
      printWindow.print();
      printWindow.close();
    };
    
    ElMessage.success('打印任务已发送');
  } catch (error) {
    ElMessage.error('打印失败：' + error.message);
  }
};

const handleSaveTemplate = () => {
  showSaveDialog.value = true;
};

const confirmSaveTemplate = async () => {
  if (!customTemplateName.value.trim()) {
    ElMessage.warning('请输入模板名称');
    return;
  }
  
  try {
    const config = {
      ...labelConfig,
      customTemplateName: customTemplateName.value
    };
    
    await colorLabelApi.saveCustomTemplate(config);
    ElMessage.success('模板保存成功');
    showSaveDialog.value = false;
    customTemplateName.value = '';
  } catch (error) {
    ElMessage.error('保存失败：' + error.message);
  }
};

const handleReset = () => {
  Object.assign(labelConfig, {
    labelText: '',
    textColor: '#0000FF',
    backgroundColor: '',
    fontFamily: '青鸟华光简行楷',
    fontSize: 40,
    fontWeight: 'normal',
    labelCount: 10
  });
  selectedTemplate.value = '';
};

const handleClosePrintPreview = () => {
  showPrintPreview.value = false;
};

const confirmPrint = () => {
  handlePrint();
  showPrintPreview.value = false;
};
</script>

<style scoped>
.color-label-designer {
  display: flex;
  gap: 20px;
  padding: 20px;
  min-height: 100vh;
}

.control-panel {
  width: 350px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.preview-panel {
  flex: 1;
}

.control-card,
.color-presets-card,
.preview-card {
  height: fit-content;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.control-group {
  margin-bottom: 15px;
}

.color-presets {
  max-height: 300px;
  overflow-y: auto;
}

.color-preset-item {
  display: flex;
  align-items: center;
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.color-preset-item:hover {
  background-color: #f5f5f5;
}

.color-preview {
  width: 20px;
  height: 20px;
  border-radius: 3px;
  border: 1px solid #ddd;
  margin-right: 10px;
}

.color-info {
  flex: 1;
}

.color-name {
  font-weight: bold;
  font-size: 14px;
}

.color-description {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.label-preview-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  min-height: 200px;
}

.label-item {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  min-width: 80px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.print-preview-content {
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  padding: 20px;
}

@media print {
  .control-panel {
    display: none;
  }
  
  .preview-panel {
    width: 100%;
  }
}
</style>
