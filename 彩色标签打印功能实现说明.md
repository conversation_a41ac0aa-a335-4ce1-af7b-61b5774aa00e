# 彩色标签批量打印功能实现说明

## 功能概述

基于旧系统 `website_backup/platformFramework/smile/color_label.html` 的彩色标签打印功能，在新系统中实现了现代化的彩色标签批量打印功能。

## 核心功能特点

### 1. 批量标签生成
- 支持一次生成1-100个相同内容的标签
- 智能布局，自动计算每行和每页标签数量
- 支持A4纸张打印优化

### 2. 实时颜色调整
- 集成颜色选择器，支持实时颜色预览
- 提供15种预设颜色方案
- 支持自定义颜色输入

### 3. 丰富的预设模板
- 移植旧系统的11个经典模板
- 包括：18k纸黄金、黄金双冠、金砖九冠、霸王花、绿砖、桃园三结义、金观音、渐变色、绿美人、天地绿、红钻之光
- 支持用户自定义模板保存

### 4. 智能字体控制
- 22种字体选择（包含中文字体）
- 智能字体大小适配（根据文字长度自动调整）
- 支持字体粗细、样式调整
- 字体大小范围：8-72px

### 5. 高级样式效果
- 阴影效果支持
- 边框样式自定义
- 渐变色背景
- 文本对齐和间距调整

### 6. 打印功能
- 浏览器原生打印支持
- 打印时自动隐藏操作界面
- 支持打印预览
- 优化的打印样式

## 技术架构

### 后端架构
```
ColorLabelController (控制器)
    ↓
ColorLabelService (服务接口)
    ↓
ColorLabelServiceImpl (服务实现)
```

### 前端架构
```
ColorLabelDesigner.vue (主组件)
    ↓
color-label-api.js (API接口)
    ↓
ColorLabelUtils (工具类)
```

## 文件结构

### 后端文件
```
payne-server/src/main/java/com/payne/server/banknote/
├── controller/
│   └── ColorLabelController.java          # 控制器
├── service/
│   ├── ColorLabelService.java             # 服务接口
│   └── impl/
│       └── ColorLabelServiceImpl.java     # 服务实现
├── entity/
│   ├── dto/
│   │   └── ColorLabelConfigDto.java       # 配置DTO
│   └── vo/
│       └── ColorLabelPreviewVO.java       # 预览VO
```

### 前端文件
```
src/views/bank-note/color-label/
├── index.vue                              # 主页面
├── components/
│   ├── ColorLabelDesigner.vue             # 设计器组件
│   ├── ColorPresets.vue                   # 颜色预设组件
│   ├── TemplateSelector.vue               # 模板选择组件
│   └── PrintPreview.vue                   # 打印预览组件
├── api/
│   └── index.js                           # API接口
└── utils/
    └── color-label-utils.js               # 工具类
```

## API接口说明

### 1. 获取预设模板
```http
GET /api/banknote/color-label/templates
```

### 2. 生成预览数据
```http
POST /api/banknote/color-label/preview
Content-Type: application/json

{
  "labelText": "标签文本",
  "textColor": "#0000FF",
  "fontFamily": "宋体",
  "fontSize": 32,
  "labelCount": 10
}
```

### 3. 批量生成打印数据
```http
POST /api/banknote/color-label/batch-generate
Content-Type: application/json

{
  "labelText": "标签文本",
  "textColor": "#0000FF",
  "fontFamily": "宋体",
  "fontSize": 32,
  "labelCount": 50
}
```

### 4. 获取字体列表
```http
GET /api/banknote/color-label/fonts
```

### 5. 获取颜色预设
```http
GET /api/banknote/color-label/colors
```

### 6. 保存自定义模板
```http
POST /api/banknote/color-label/template
Content-Type: application/json

{
  "customTemplateName": "我的模板",
  "labelText": "标签文本",
  "textColor": "#0000FF",
  "fontFamily": "宋体",
  "fontSize": 32
}
```

## 使用方法

### 1. 基本使用
1. 在文本框中输入标签内容
2. 选择文字颜色
3. 设置字体和字号
4. 设置标签数量
5. 点击"预览"查看效果
6. 点击"打印"进行批量打印

### 2. 使用预设模板
1. 在"预设模板"下拉框中选择模板
2. 系统自动应用模板样式
3. 可在此基础上进行微调
4. 点击"打印"进行批量打印

### 3. 保存自定义模板
1. 设计好标签样式
2. 点击"保存模板"按钮
3. 输入模板名称
4. 确认保存

### 4. 高级功能
- 使用颜色预设表快速选择颜色
- 使用智能字体调整功能
- 启用阴影、边框、渐变等特效

## 兼容性说明

### 浏览器支持
- Chrome（推荐）
- Firefox
- Safari
- Edge

### 打印支持
- 支持所有现代浏览器的打印功能
- 自动优化打印样式
- 支持A4纸张规格

## 部署说明

### 后端部署
1. 将Java文件放入对应目录
2. 确保Spring Boot自动扫描到相关类
3. 配置权限控制（如需要）

### 前端部署
1. 将Vue组件放入对应目录
2. 配置路由（如需要）
3. 确保API接口地址正确

## 扩展功能

### 已实现
- [x] 基础标签设计
- [x] 颜色选择器
- [x] 预设模板
- [x] 字体控制
- [x] 批量打印
- [x] 打印预览
- [x] 自定义模板保存

### 可扩展
- [ ] 图片插入功能
- [ ] 二维码生成
- [ ] 条形码支持
- [ ] 更多特效样式
- [ ] 模板分享功能
- [ ] 批量导入数据

## 注意事项

1. **字体支持**：确保系统安装了相应的中文字体
2. **打印设置**：建议使用Chrome浏览器获得最佳打印效果
3. **性能考虑**：大量标签（>50个）时建议分批打印
4. **权限控制**：根据需要配置相应的权限注解

## 维护说明

### 添加新的预设模板
在 `ColorLabelServiceImpl.getPresetTemplates()` 方法中添加新模板：

```java
templates.add(createTemplate("新模板名", "#颜色值", "字体名", "HTML内容"));
```

### 添加新的字体
在 `ColorLabelServiceImpl.getFontList()` 方法中添加新字体：

```java
fonts.add(createFont("字体显示名", "字体CSS值"));
```

### 添加新的颜色预设
在 `ColorLabelServiceImpl.getColorPresets()` 方法中添加新颜色：

```java
colors.add(createColor("颜色名", "#颜色值", "描述信息"));
```

## 总结

本实现完全基于旧系统的功能特点，使用现代化的技术栈重新构建，保持了原有的易用性和功能完整性，同时提供了更好的用户体验和扩展性。
