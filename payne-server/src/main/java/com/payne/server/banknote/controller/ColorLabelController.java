package com.payne.server.banknote.controller;

import com.payne.common.core.api.ApiResult;
import com.payne.common.core.web.BaseController;
import com.payne.server.banknote.entity.dto.ColorLabelConfigDto;
import com.payne.server.banknote.entity.vo.ColorLabelPreviewVO;
import com.payne.server.banknote.service.ColorLabelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 彩色标签打印控制器
 * 
 * <AUTHOR>
 * @since 2025-08-03
 */
@Api(tags = "彩色标签打印")
@RestController
@RequestMapping("/api/color-label")
@RequiredArgsConstructor
public class ColorLabelController extends BaseController {

    private final ColorLabelService colorLabelService;

    /**
     * 获取预设模板列表
     */
    @ApiOperation("获取预设模板列表")
    @GetMapping("/templates")
    @PreAuthorize("hasAuthority('banknote:color-label:view')")
    public ApiResult<List<Map<String, Object>>> getPresetTemplates() {
        try {
            List<Map<String, Object>> templates = colorLabelService.getPresetTemplates();
            return success("获取成功", templates);
        } catch (Exception e) {
            return fail("获取失败：" + e.getMessage());
        }
    }

    /**
     * 生成彩色标签预览数据
     */
    @ApiOperation("生成彩色标签预览数据")
    @PostMapping("/preview")
    @PreAuthorize("hasAuthority('banknote:color-label:view')")
    public ApiResult<ColorLabelPreviewVO> generatePreview(
            @ApiParam("标签配置") @Valid @RequestBody ColorLabelConfigDto config) {
        try {
            ColorLabelPreviewVO preview = colorLabelService.generatePreview(config);
            return success("生成成功", preview);
        } catch (Exception e) {
            return fail("生成失败：" + e.getMessage());
        }
    }

    /**
     * 批量生成彩色标签打印数据
     */
    @ApiOperation("批量生成彩色标签打印数据")
    @PostMapping("/batch-generate")
    @PreAuthorize("hasAuthority('banknote:color-label:print')")
    public ApiResult<Map<String, Object>> batchGenerate(
            @ApiParam("标签配置") @Valid @RequestBody ColorLabelConfigDto config) {
        try {
            Map<String, Object> printData = colorLabelService.batchGenerate(config);
            return success("生成成功", printData);
        } catch (Exception e) {
            return fail("生成失败：" + e.getMessage());
        }
    }

    /**
     * 获取字体列表
     */
    @ApiOperation("获取字体列表")
    @GetMapping("/fonts")
    @PreAuthorize("hasAuthority('banknote:color-label:view')")
    public ApiResult<List<Map<String, String>>> getFontList() {
        try {
            List<Map<String, String>> fonts = colorLabelService.getFontList();
            return success("获取成功", fonts);
        } catch (Exception e) {
            return fail("获取失败：" + e.getMessage());
        }
    }

    /**
     * 获取颜色预设
     */
    @ApiOperation("获取颜色预设")
    @GetMapping("/colors")
    @PreAuthorize("hasAuthority('banknote:color-label:view')")
    public ApiResult<List<Map<String, String>>> getColorPresets() {
        try {
            List<Map<String, String>> colors = colorLabelService.getColorPresets();
            return success("获取成功", colors);
        } catch (Exception e) {
            return fail("获取失败：" + e.getMessage());
        }
    }

    /**
     * 保存用户自定义模板
     */
    @ApiOperation("保存用户自定义模板")
    @PostMapping("/template")
    @PreAuthorize("hasAuthority('banknote:color-label:manage')")
    public ApiResult<?> saveCustomTemplate(
            @ApiParam("模板配置") @Valid @RequestBody ColorLabelConfigDto config) {
        try {
            colorLabelService.saveCustomTemplate(config);
            return success("保存成功");
        } catch (Exception e) {
            return fail("保存失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户自定义模板列表
     */
    @ApiOperation("获取用户自定义模板列表")
    @GetMapping("/custom-templates")
    @PreAuthorize("hasAuthority('banknote:color-label:view')")
    public ApiResult<List<Map<String, Object>>> getCustomTemplates() {
        try {
            List<Map<String, Object>> templates = colorLabelService.getCustomTemplates();
            return success("获取成功", templates);
        } catch (Exception e) {
            return fail("获取失败：" + e.getMessage());
        }
    }

    /**
     * 删除用户自定义模板
     */
    @ApiOperation("删除用户自定义模板")
    @DeleteMapping("/template/{id}")
    @PreAuthorize("hasAuthority('banknote:color-label:manage')")
    public ApiResult<?> deleteCustomTemplate(
            @ApiParam("模板ID") @PathVariable String id) {
        try {
            colorLabelService.deleteCustomTemplate(id);
            return success("删除成功");
        } catch (Exception e) {
            return fail("删除失败：" + e.getMessage());
        }
    }
}
