package com.payne.server.banknote.controller;

import com.payne.core.annotation.OperationLog;
import com.payne.core.web.ApiResult;
import com.payne.core.web.BaseController;
import com.payne.server.banknote.entity.dto.ColorLabelConfigDto;
import com.payne.server.banknote.entity.vo.ColorLabelPreviewVO;
import com.payne.server.banknote.service.ColorLabelService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 彩色标签打印控制器
 *
 * <AUTHOR>
 * @since 2025-08-03
 */
@RestController
@RequestMapping("/api/banknote/color-label")
public class ColorLabelController extends BaseController {

    @Resource
    private ColorLabelService colorLabelService;

    /**
     * 获取预设模板列表
     */
    @PreAuthorize("hasAuthority('banknote:color-label:view')")
    @OperationLog
    @GetMapping("/templates")
    public ApiResult<?> getPresetTemplates() {
        try {
            List<Map<String, Object>> templates = colorLabelService.getPresetTemplates();
            return success("获取成功", templates);
        } catch (Exception e) {
            return fail("获取失败：" + e.getMessage());
        }
    }

    /**
     * 生成彩色标签预览数据
     */
    @PreAuthorize("hasAuthority('banknote:color-label:view')")
    @OperationLog
    @PostMapping("/preview")
    public ApiResult<?> generatePreview(@Valid @RequestBody ColorLabelConfigDto config) {
        try {
            ColorLabelPreviewVO preview = colorLabelService.generatePreview(config);
            return success("生成成功", preview);
        } catch (Exception e) {
            return fail("生成失败：" + e.getMessage());
        }
    }

    /**
     * 批量生成彩色标签打印数据
     */
    @PreAuthorize("hasAuthority('banknote:color-label:print')")
    @OperationLog
    @PostMapping("/batch-generate")
    public ApiResult<?> batchGenerate(@Valid @RequestBody ColorLabelConfigDto config) {
        try {
            Map<String, Object> printData = colorLabelService.batchGenerate(config);
            return success("生成成功", printData);
        } catch (Exception e) {
            return fail("生成失败：" + e.getMessage());
        }
    }

    /**
     * 获取字体列表
     */
    @PreAuthorize("hasAuthority('banknote:color-label:view')")
    @OperationLog
    @GetMapping("/fonts")
    public ApiResult<?> getFontList() {
        try {
            List<Map<String, String>> fonts = colorLabelService.getFontList();
            return success("获取成功", fonts);
        } catch (Exception e) {
            return fail("获取失败：" + e.getMessage());
        }
    }

    /**
     * 获取颜色预设
     */
    @PreAuthorize("hasAuthority('banknote:color-label:view')")
    @OperationLog
    @GetMapping("/colors")
    public ApiResult<?> getColorPresets() {
        try {
            List<Map<String, String>> colors = colorLabelService.getColorPresets();
            return success("获取成功", colors);
        } catch (Exception e) {
            return fail("获取失败：" + e.getMessage());
        }
    }

    /**
     * 保存用户自定义模板
     */
    @PreAuthorize("hasAuthority('banknote:color-label:manage')")
    @OperationLog(module = "彩色标签", comments = "保存自定义模板")
    @PostMapping("/template")
    public ApiResult<?> saveCustomTemplate(@Valid @RequestBody ColorLabelConfigDto config) {
        try {
            colorLabelService.saveCustomTemplate(config);
            return success("保存成功");
        } catch (Exception e) {
            return fail("保存失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户自定义模板列表
     */
    @PreAuthorize("hasAuthority('banknote:color-label:view')")
    @OperationLog
    @GetMapping("/custom-templates")
    public ApiResult<?> getCustomTemplates() {
        try {
            List<Map<String, Object>> templates = colorLabelService.getCustomTemplates();
            return success("获取成功", templates);
        } catch (Exception e) {
            return fail("获取失败：" + e.getMessage());
        }
    }

    /**
     * 删除用户自定义模板
     */
    @PreAuthorize("hasAuthority('banknote:color-label:manage')")
    @OperationLog(module = "彩色标签", comments = "删除自定义模板")
    @DeleteMapping("/template/{id}")
    public ApiResult<?> deleteCustomTemplate(@PathVariable String id) {
        try {
            colorLabelService.deleteCustomTemplate(id);
            return success("删除成功");
        } catch (Exception e) {
            return fail("删除失败：" + e.getMessage());
        }
    }
}
