package com.payne.server.banknote.service.impl;

import com.payne.server.banknote.entity.dto.ColorLabelConfigDto;
import com.payne.server.banknote.entity.vo.ColorLabelPreviewVO;
import com.payne.server.banknote.service.ColorLabelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 彩色标签服务实现类
 * 
 * <AUTHOR>
 * @since 2025-08-03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ColorLabelServiceImpl implements ColorLabelService {

    @Override
    public List<Map<String, Object>> getPresetTemplates() {
        List<Map<String, Object>> templates = new ArrayList<>();
        
        // 基于旧系统的预设模板
        templates.add(createTemplate("18k纸黄金", "#FFD700", "宋体", 
            "<span style=\"font-family:宋体;\">18k</span><br><span>纸黄金</span>"));
        
        templates.add(createTemplate("黄金双冠", "#FFD700", "宋体", 
            "<span>黄金双冠</span><br><span></span>"));
        
        templates.add(createTemplate("金砖九冠", "#FFD700", "宋体", 
            "<span>金砖九冠</span><br><span></span>"));
        
        templates.add(createTemplate("霸王花", "#FF0000", "楷体", 
            "<img src='/images/霸王花.jpg' width='10px;' height='20px;'><span style='font-family: 楷体;color: red'>霸王花</span><br><span></span>"));
        
        templates.add(createTemplate("绿砖", "#00FF00", "宋体", 
            "<font style='font-family:宋体; color:#00ff00'>绿钻</font>"));
        
        templates.add(createTemplate("桃园三结义", "#00B83F", "华文行楷", 
            "<font style='font-family:华文行楷; color: #00B83F'>桃园三结义</font>"));
        
        templates.add(createTemplate("金观音", "#aa8c30", "华文行楷", 
            "<font style='font-family:华文行楷; color: #aa8c30'>金观音</font>"));
        
        templates.add(createTemplate("渐变色", "#C0003F", "宋体", 
            "<font color=#C0003F>十</font><font color=#81007E>全</font><font color=#4200BD>十</font><font color=#0300FC>美</font>"));
        
        templates.add(createTemplate("绿美人", "#0D823B", "宋体", 
            "<font color=#0D823B>绿</font><font color=#1A9240>美</font><font color=#27A245>人</font><br>"));
        
        templates.add(createTemplate("天地绿", "#34B147", "宋体", 
            "<font color=#34B147>天</font><font color=#31AE44>地</font><font color=#2EAB41>绿</font><br>"));
        
        templates.add(createTemplate("红钻之光", "#f16d7e", "宋体", 
            "<span style=\"color:#f16d7e;text-shadow:0 0 1px #ff0000\">红钻之光</span>"));
        
        return templates;
    }

    @Override
    public ColorLabelPreviewVO generatePreview(ColorLabelConfigDto config) {
        ColorLabelPreviewVO preview = new ColorLabelPreviewVO();
        
        // 生成预览HTML
        String previewHtml = generateLabelHtml(config);
        preview.setPreviewHtml(previewHtml);
        
        // 生成预览CSS
        String previewCss = generateLabelCss(config);
        preview.setPreviewCss(previewCss);
        
        // 设置标签配置信息
        Map<String, Object> labelConfig = new HashMap<>();
        labelConfig.put("text", config.getLabelText());
        labelConfig.put("color", config.getTextColor());
        labelConfig.put("fontFamily", config.getFontFamily());
        labelConfig.put("fontSize", config.getFontSize());
        labelConfig.put("count", config.getLabelCount());
        preview.setLabelConfig(labelConfig);
        
        // 生成标签项列表
        List<ColorLabelPreviewVO.LabelItem> labelItems = new ArrayList<>();
        for (int i = 0; i < config.getLabelCount(); i++) {
            ColorLabelPreviewVO.LabelItem item = new ColorLabelPreviewVO.LabelItem();
            item.setLabelId("label_" + (i + 1));
            item.setContent(config.getLabelText());
            item.setStyle(generateItemStyle(config));
            item.setHtml(generateItemHtml(config));
            labelItems.add(item);
        }
        preview.setLabelItems(labelItems);
        
        // 设置打印配置
        ColorLabelPreviewVO.PrintConfig printConfig = new ColorLabelPreviewVO.PrintConfig();
        printConfig.setPageWidth(210.0); // A4宽度
        printConfig.setPageHeight(297.0); // A4高度
        printConfig.setMargin(10.0);
        printConfig.setLabelsPerRow(calculateLabelsPerRow(config));
        printConfig.setLabelsPerPage(calculateLabelsPerPage(config));
        printConfig.setLabelSpacing(5.0);
        preview.setPrintConfig(printConfig);
        
        return preview;
    }

    @Override
    public Map<String, Object> batchGenerate(ColorLabelConfigDto config) {
        Map<String, Object> result = new HashMap<>();
        
        // 生成批量打印HTML
        String batchHtml = generateBatchPrintHtml(config);
        result.put("html", batchHtml);
        
        // 生成批量打印CSS
        String batchCss = generateBatchPrintCss(config);
        result.put("css", batchCss);
        
        // 设置打印配置
        Map<String, Object> printConfig = new HashMap<>();
        printConfig.put("pageSize", "A4");
        printConfig.put("orientation", "portrait");
        printConfig.put("margin", "10mm");
        printConfig.put("labelCount", config.getLabelCount());
        result.put("printConfig", printConfig);
        
        // 生成标签数据
        List<Map<String, Object>> labels = new ArrayList<>();
        for (int i = 0; i < config.getLabelCount(); i++) {
            Map<String, Object> label = new HashMap<>();
            label.put("id", i + 1);
            label.put("content", config.getLabelText());
            label.put("style", generateItemStyle(config));
            labels.add(label);
        }
        result.put("labels", labels);
        
        return result;
    }

    @Override
    public List<Map<String, String>> getFontList() {
        List<Map<String, String>> fonts = new ArrayList<>();
        
        // 基于旧系统的字体列表
        fonts.add(createFont("青鸟华光简行楷", "青鸟华光简行楷"));
        fonts.add(createFont("宋体", "SimSun"));
        fonts.add(createFont("黑体", "SimHei"));
        fonts.add(createFont("微软雅黑", "Microsoft Yahei"));
        fonts.add(createFont("微软正黑体", "Microsoft JhengHei"));
        fonts.add(createFont("楷体", "KaiTi"));
        fonts.add(createFont("新宋体", "NSimSun"));
        fonts.add(createFont("仿宋", "FangSong"));
        fonts.add(createFont("华文楷体", "STKaiti"));
        fonts.add(createFont("华文宋体", "STSong"));
        fonts.add(createFont("华文仿宋", "STFangsong"));
        fonts.add(createFont("华文中宋", "STZhongsong"));
        fonts.add(createFont("华文琥珀", "STHupo"));
        fonts.add(createFont("华文新魏", "STXinwei"));
        fonts.add(createFont("华文隶书", "STLiti"));
        fonts.add(createFont("华文行楷", "STXingkai"));
        fonts.add(createFont("幼圆", "YouYuan"));
        fonts.add(createFont("隶书", "LiSu"));
        fonts.add(createFont("华文细黑", "STXihei"));
        fonts.add(createFont("华文彩云", "STCaiyun"));
        fonts.add(createFont("方正舒体", "FZShuTi"));
        fonts.add(createFont("方正姚体", "FZYaoti"));
        
        return fonts;
    }

    @Override
    public List<Map<String, String>> getColorPresets() {
        List<Map<String, String>> colors = new ArrayList<>();
        
        // 基于旧系统的颜色预设
        colors.add(createColor("大红色", "#FF0000", "纵二横三：补号，幼线体，数字冠，大王冠，首发冠，平水，凸版，渡水，中水，小圆水，大圆水，爱情号，长号，宽水红，爱情号，豹子号，老虎号，中国梦，红二平，生日快乐，爱版，窄水红，红光蓝鹤"));
        colors.add(createColor("深红色", "#980000", "纵二横二：浴火凤凰"));
        colors.add(createColor("黑色", "#000000", "纵一：满堂彩，炭黑，深版"));
        colors.add(createColor("绿色", "#00FF00", "纵三：绿幽灵，绿钻"));
        colors.add(createColor("背绿", "#95db95", ""));
        colors.add(createColor("深青色", "#B9D7A8", "纵七：青绿美翠，五彩苍松，苍松翠鹤"));
        colors.add(createColor("浅蓝色", "#0096db", "玉钩国"));
        colors.add(createColor("天蓝色", "#00FFFF", "纵三横二：天蓝色，蓝凤朝阳"));
        colors.add(createColor("蓝色", "#4A86E8", "纵二横一：蓝天白云，蓝色妖姬"));
        colors.add(createColor("深蓝色", "#0000FF", "纵一横一：深蓝"));
        colors.add(createColor("紫色", "#9900FF", "纵一横二：紫气东来"));
        colors.add(createColor("粉色", "#FF69B4", "纵二横五：粉色佳人"));
        colors.add(createColor("桃红色", "#FF1493", "金杯桃花红"));
        colors.add(createColor("橙色", "#FF9900", "纵二横四：金牡丹，红金龙，金龙王，金光蓝鹤"));
        colors.add(createColor("金星绿波", "#e68e09", ""));
        
        return colors;
    }

    @Override
    public void saveCustomTemplate(ColorLabelConfigDto config) {
        // TODO: 实现保存用户自定义模板到数据库
        log.info("保存用户自定义模板: {}", config.getCustomTemplateName());
    }

    @Override
    public List<Map<String, Object>> getCustomTemplates() {
        // TODO: 从数据库获取用户自定义模板
        return new ArrayList<>();
    }

    @Override
    public void deleteCustomTemplate(String id) {
        // TODO: 从数据库删除用户自定义模板
        log.info("删除用户自定义模板: {}", id);
    }

    // 私有辅助方法
    private Map<String, Object> createTemplate(String name, String color, String font, String content) {
        Map<String, Object> template = new HashMap<>();
        template.put("name", name);
        template.put("color", color);
        template.put("fontFamily", font);
        template.put("content", content);
        template.put("preview", generateTemplatePreview(content, color, font));
        return template;
    }

    private Map<String, String> createFont(String name, String value) {
        Map<String, String> font = new HashMap<>();
        font.put("name", name);
        font.put("value", value);
        return font;
    }

    private Map<String, String> createColor(String name, String value, String description) {
        Map<String, String> color = new HashMap<>();
        color.put("name", name);
        color.put("value", value);
        color.put("description", description);
        return color;
    }

    private String generateTemplatePreview(String content, String color, String font) {
        return String.format(
            "<div style=\"color: %s; font-family: %s; font-size: 14px; padding: 5px;\">%s</div>",
            color, font, content
        );
    }

    private String generateLabelHtml(ColorLabelConfigDto config) {
        StringBuilder html = new StringBuilder();
        html.append("<div class=\"color-label-preview\">");
        
        for (int i = 0; i < Math.min(config.getLabelCount(), 10); i++) { // 预览最多显示10个
            html.append("<div class=\"label-item\">")
                .append(config.getLabelText())
                .append("</div>");
        }
        
        html.append("</div>");
        return html.toString();
    }

    private String generateLabelCss(ColorLabelConfigDto config) {
        StringBuilder css = new StringBuilder();
        css.append(".color-label-preview { display: flex; flex-wrap: wrap; gap: 10px; }")
           .append(".label-item { ")
           .append("color: ").append(config.getTextColor()).append("; ")
           .append("font-family: ").append(config.getFontFamily()).append("; ")
           .append("font-size: ").append(config.getFontSize()).append("px; ");
        
        if (config.getFontWeight() != null) {
            css.append("font-weight: ").append(config.getFontWeight()).append("; ");
        }
        
        if (config.getBackgroundColor() != null) {
            css.append("background-color: ").append(config.getBackgroundColor()).append("; ");
        }
        
        css.append("padding: 5px 10px; border: 1px solid #ccc; }");
        
        return css.toString();
    }

    private String generateItemStyle(ColorLabelConfigDto config) {
        StringBuilder style = new StringBuilder();
        style.append("color: ").append(config.getTextColor()).append("; ")
             .append("font-family: ").append(config.getFontFamily()).append("; ")
             .append("font-size: ").append(config.getFontSize()).append("px; ");
        
        if (config.getFontWeight() != null) {
            style.append("font-weight: ").append(config.getFontWeight()).append("; ");
        }
        
        if (config.getBackgroundColor() != null) {
            style.append("background-color: ").append(config.getBackgroundColor()).append("; ");
        }
        
        return style.toString();
    }

    private String generateItemHtml(ColorLabelConfigDto config) {
        return String.format(
            "<div style=\"%s\">%s</div>",
            generateItemStyle(config),
            config.getLabelText()
        );
    }

    private String generateBatchPrintHtml(ColorLabelConfigDto config) {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html><html><head><meta charset=\"UTF-8\">")
            .append("<title>彩色标签批量打印</title>")
            .append("<style>").append(generateBatchPrintCss(config)).append("</style>")
            .append("</head><body>");
        
        html.append("<div class=\"print-container\">");
        
        for (int i = 0; i < config.getLabelCount(); i++) {
            html.append("<div class=\"print-label\">")
                .append(config.getLabelText())
                .append("</div>");
        }
        
        html.append("</div></body></html>");
        
        return html.toString();
    }

    private String generateBatchPrintCss(ColorLabelConfigDto config) {
        StringBuilder css = new StringBuilder();
        css.append("@page { size: A4; margin: 10mm; }")
           .append("body { margin: 0; padding: 0; font-family: Arial, sans-serif; }")
           .append(".print-container { display: flex; flex-wrap: wrap; gap: 5mm; }")
           .append(".print-label { ")
           .append("color: ").append(config.getTextColor()).append("; ")
           .append("font-family: ").append(config.getFontFamily()).append("; ")
           .append("font-size: ").append(config.getFontSize()).append("px; ")
           .append("padding: 3mm; ")
           .append("border: 1px solid #ccc; ")
           .append("width: 40mm; ")
           .append("height: 20mm; ")
           .append("display: flex; ")
           .append("align-items: center; ")
           .append("justify-content: center; ")
           .append("text-align: center; ");
        
        if (config.getBackgroundColor() != null) {
            css.append("background-color: ").append(config.getBackgroundColor()).append("; ");
        }
        
        css.append("}");
        
        return css.toString();
    }

    private Integer calculateLabelsPerRow(ColorLabelConfigDto config) {
        // 基于A4纸张宽度计算每行标签数量
        double pageWidth = 190.0; // A4宽度减去边距
        double labelWidth = 40.0; // 标签宽度
        double spacing = 5.0; // 标签间距
        
        return (int) Math.floor((pageWidth + spacing) / (labelWidth + spacing));
    }

    private Integer calculateLabelsPerPage(ColorLabelConfigDto config) {
        // 基于A4纸张高度计算每页标签数量
        double pageHeight = 277.0; // A4高度减去边距
        double labelHeight = 20.0; // 标签高度
        double spacing = 5.0; // 标签间距
        
        int labelsPerRow = calculateLabelsPerRow(config);
        int rowsPerPage = (int) Math.floor((pageHeight + spacing) / (labelHeight + spacing));
        
        return labelsPerRow * rowsPerPage;
    }
}
