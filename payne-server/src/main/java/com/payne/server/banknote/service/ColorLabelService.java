package com.payne.server.banknote.service;

import com.payne.server.banknote.entity.dto.ColorLabelConfigDto;
import com.payne.server.banknote.entity.vo.ColorLabelPreviewVO;

import java.util.List;
import java.util.Map;

/**
 * 彩色标签服务接口
 * 
 * <AUTHOR>
 * @since 2025-08-03
 */
public interface ColorLabelService {

    /**
     * 获取预设模板列表
     * 
     * @return 预设模板列表
     */
    List<Map<String, Object>> getPresetTemplates();

    /**
     * 生成彩色标签预览数据
     * 
     * @param config 标签配置
     * @return 预览数据
     */
    ColorLabelPreviewVO generatePreview(ColorLabelConfigDto config);

    /**
     * 批量生成彩色标签打印数据
     * 
     * @param config 标签配置
     * @return 打印数据
     */
    Map<String, Object> batchGenerate(ColorLabelConfigDto config);

    /**
     * 获取字体列表
     * 
     * @return 字体列表
     */
    List<Map<String, String>> getFontList();

    /**
     * 获取颜色预设
     * 
     * @return 颜色预设列表
     */
    List<Map<String, String>> getColorPresets();

    /**
     * 保存用户自定义模板
     * 
     * @param config 模板配置
     */
    void saveCustomTemplate(ColorLabelConfigDto config);

    /**
     * 获取用户自定义模板列表
     * 
     * @return 自定义模板列表
     */
    List<Map<String, Object>> getCustomTemplates();

    /**
     * 删除用户自定义模板
     * 
     * @param id 模板ID
     */
    void deleteCustomTemplate(String id);
}
