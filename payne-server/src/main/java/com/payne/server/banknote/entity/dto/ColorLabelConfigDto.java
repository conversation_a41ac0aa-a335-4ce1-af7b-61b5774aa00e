package com.payne.server.banknote.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 彩色标签配置DTO
 * 
 * <AUTHOR>
 * @since 2025-08-03
 */
@Data
@ApiModel("彩色标签配置")
public class ColorLabelConfigDto {

    @ApiModelProperty("标签文本内容")
    @NotBlank(message = "标签文本不能为空")
    private String labelText;

    @ApiModelProperty("文字颜色（十六进制）")
    @NotBlank(message = "文字颜色不能为空")
    private String textColor;

    @ApiModelProperty("背景颜色（十六进制）")
    private String backgroundColor;

    @ApiModelProperty("字体名称")
    @NotBlank(message = "字体名称不能为空")
    private String fontFamily;

    @ApiModelProperty("字体大小")
    @NotNull(message = "字体大小不能为空")
    @Min(value = 8, message = "字体大小不能小于8")
    @Max(value = 72, message = "字体大小不能大于72")
    private Integer fontSize;

    @ApiModelProperty("字体粗细")
    private String fontWeight;

    @ApiModelProperty("字体样式（normal/italic）")
    private String fontStyle;

    @ApiModelProperty("文本对齐方式（left/center/right）")
    private String textAlign;

    @ApiModelProperty("行高")
    private Double lineHeight;

    @ApiModelProperty("字间距")
    private Double letterSpacing;

    @ApiModelProperty("标签数量")
    @NotNull(message = "标签数量不能为空")
    @Min(value = 1, message = "标签数量不能小于1")
    @Max(value = 100, message = "标签数量不能大于100")
    private Integer labelCount;

    @ApiModelProperty("预设模板名称")
    private String templateName;

    @ApiModelProperty("是否使用预设模板")
    private Boolean usePresetTemplate;

    @ApiModelProperty("自定义CSS样式")
    private String customCss;

    @ApiModelProperty("标签尺寸配置")
    private LabelSizeConfig sizeConfig;

    @ApiModelProperty("是否启用阴影效果")
    private Boolean enableShadow;

    @ApiModelProperty("阴影配置")
    private ShadowConfig shadowConfig;

    @ApiModelProperty("是否启用边框")
    private Boolean enableBorder;

    @ApiModelProperty("边框配置")
    private BorderConfig borderConfig;

    @ApiModelProperty("是否启用渐变色")
    private Boolean enableGradient;

    @ApiModelProperty("渐变色配置")
    private GradientConfig gradientConfig;

    @ApiModelProperty("用户自定义模板名称（保存时使用）")
    private String customTemplateName;

    @ApiModelProperty("钱币ID列表（用于批量打印）")
    private List<String> coinIds;

    /**
     * 标签尺寸配置
     */
    @Data
    @ApiModel("标签尺寸配置")
    public static class LabelSizeConfig {
        @ApiModelProperty("宽度（mm）")
        private Double width;

        @ApiModelProperty("高度（mm）")
        private Double height;

        @ApiModelProperty("内边距（mm）")
        private Double padding;
    }

    /**
     * 阴影配置
     */
    @Data
    @ApiModel("阴影配置")
    public static class ShadowConfig {
        @ApiModelProperty("水平偏移")
        private Integer offsetX;

        @ApiModelProperty("垂直偏移")
        private Integer offsetY;

        @ApiModelProperty("模糊半径")
        private Integer blurRadius;

        @ApiModelProperty("阴影颜色")
        private String shadowColor;
    }

    /**
     * 边框配置
     */
    @Data
    @ApiModel("边框配置")
    public static class BorderConfig {
        @ApiModelProperty("边框宽度")
        private Integer borderWidth;

        @ApiModelProperty("边框样式（solid/dashed/dotted）")
        private String borderStyle;

        @ApiModelProperty("边框颜色")
        private String borderColor;

        @ApiModelProperty("边框圆角")
        private Integer borderRadius;
    }

    /**
     * 渐变色配置
     */
    @Data
    @ApiModel("渐变色配置")
    public static class GradientConfig {
        @ApiModelProperty("渐变类型（linear/radial）")
        private String gradientType;

        @ApiModelProperty("渐变方向（仅线性渐变）")
        private String gradientDirection;

        @ApiModelProperty("渐变色彩停止点")
        private List<ColorStop> colorStops;

        @Data
        @ApiModel("渐变色彩停止点")
        public static class ColorStop {
            @ApiModelProperty("颜色")
            private String color;

            @ApiModelProperty("位置（0-100）")
            private Integer position;
        }
    }
}
