package com.payne.server.banknote.entity.dto;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 彩色标签配置DTO
 *
 * <AUTHOR>
 * @since 2025-08-03
 */
@Data
public class ColorLabelConfigDto {

    /**
     * 标签文本内容
     */
    @NotBlank(message = "标签文本不能为空")
    private String labelText;

    /**
     * 文字颜色（十六进制）
     */
    @NotBlank(message = "文字颜色不能为空")
    private String textColor;

    /**
     * 背景颜色（十六进制）
     */
    private String backgroundColor;

    /**
     * 字体名称
     */
    @NotBlank(message = "字体名称不能为空")
    private String fontFamily;

    /**
     * 字体大小
     */
    @NotNull(message = "字体大小不能为空")
    @Min(value = 8, message = "字体大小不能小于8")
    @Max(value = 72, message = "字体大小不能大于72")
    private Integer fontSize;

    /**
     * 字体粗细
     */
    private String fontWeight;

    /**
     * 字体样式（normal/italic）
     */
    private String fontStyle;

    /**
     * 文本对齐方式（left/center/right）
     */
    private String textAlign;

    /**
     * 行高
     */
    private Double lineHeight;

    /**
     * 字间距
     */
    private Double letterSpacing;

    /**
     * 标签数量
     */
    @NotNull(message = "标签数量不能为空")
    @Min(value = 1, message = "标签数量不能小于1")
    @Max(value = 100, message = "标签数量不能大于100")
    private Integer labelCount;

    /**
     * 预设模板名称
     */
    private String templateName;

    /**
     * 是否使用预设模板
     */
    private Boolean usePresetTemplate;

    /**
     * 自定义CSS样式
     */
    private String customCss;

    /**
     * 标签尺寸配置
     */
    private LabelSizeConfig sizeConfig;

    /**
     * 是否启用阴影效果
     */
    private Boolean enableShadow;

    /**
     * 阴影配置
     */
    private ShadowConfig shadowConfig;

    /**
     * 是否启用边框
     */
    private Boolean enableBorder;

    /**
     * 边框配置
     */
    private BorderConfig borderConfig;

    /**
     * 是否启用渐变色
     */
    private Boolean enableGradient;

    /**
     * 渐变色配置
     */
    private GradientConfig gradientConfig;

    /**
     * 用户自定义模板名称（保存时使用）
     */
    private String customTemplateName;

    /**
     * 钱币ID列表（用于批量打印）
     */
    private List<String> coinIds;

    /**
     * 标签尺寸配置
     */
    @Data
    public static class LabelSizeConfig {
        /**
         * 宽度（mm）
         */
        private Double width;

        /**
         * 高度（mm）
         */
        private Double height;

        /**
         * 内边距（mm）
         */
        private Double padding;
    }

    /**
     * 阴影配置
     */
    @Data
    public static class ShadowConfig {
        /**
         * 水平偏移
         */
        private Integer offsetX;

        /**
         * 垂直偏移
         */
        private Integer offsetY;

        /**
         * 模糊半径
         */
        private Integer blurRadius;

        /**
         * 阴影颜色
         */
        private String shadowColor;
    }

    /**
     * 边框配置
     */
    @Data
    public static class BorderConfig {
        /**
         * 边框宽度
         */
        private Integer borderWidth;

        /**
         * 边框样式（solid/dashed/dotted）
         */
        private String borderStyle;

        /**
         * 边框颜色
         */
        private String borderColor;

        /**
         * 边框圆角
         */
        private Integer borderRadius;
    }

    /**
     * 渐变色配置
     */
    @Data
    public static class GradientConfig {
        /**
         * 渐变类型（linear/radial）
         */
        private String gradientType;

        /**
         * 渐变方向（仅线性渐变）
         */
        private String gradientDirection;

        /**
         * 渐变色彩停止点
         */
        private List<ColorStop> colorStops;

        /**
         * 渐变色彩停止点
         */
        @Data
        public static class ColorStop {
            /**
             * 颜色
             */
            private String color;

            /**
             * 位置（0-100）
             */
            private Integer position;
        }
    }
}
