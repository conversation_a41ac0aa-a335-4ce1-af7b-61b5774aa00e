package com.payne.server.banknote.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 彩色标签预览VO
 * 
 * <AUTHOR>
 * @since 2025-08-03
 */
@Data
@ApiModel("彩色标签预览")
public class ColorLabelPreviewVO {

    @ApiModelProperty("预览HTML内容")
    private String previewHtml;

    @ApiModelProperty("预览CSS样式")
    private String previewCss;

    @ApiModelProperty("标签配置信息")
    private Map<String, Object> labelConfig;

    @ApiModelProperty("标签列表")
    private List<LabelItem> labelItems;

    @ApiModelProperty("打印配置")
    private PrintConfig printConfig;

    /**
     * 标签项
     */
    @Data
    @ApiModel("标签项")
    public static class LabelItem {
        @ApiModelProperty("标签ID")
        private String labelId;

        @ApiModelProperty("标签内容")
        private String content;

        @ApiModelProperty("标签样式")
        private String style;

        @ApiModelProperty("标签HTML")
        private String html;
    }

    /**
     * 打印配置
     */
    @Data
    @ApiModel("打印配置")
    public static class PrintConfig {
        @ApiModelProperty("页面宽度（mm）")
        private Double pageWidth;

        @ApiModelProperty("页面高度（mm）")
        private Double pageHeight;

        @ApiModelProperty("页边距（mm）")
        private Double margin;

        @ApiModelProperty("每行标签数量")
        private Integer labelsPerRow;

        @ApiModelProperty("每页标签数量")
        private Integer labelsPerPage;

        @ApiModelProperty("标签间距（mm）")
        private Double labelSpacing;
    }
}
