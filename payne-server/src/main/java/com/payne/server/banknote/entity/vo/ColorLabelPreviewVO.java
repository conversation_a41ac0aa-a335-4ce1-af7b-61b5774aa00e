package com.payne.server.banknote.entity.vo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 彩色标签预览VO
 *
 * <AUTHOR>
 * @since 2025-08-03
 */
@Data
public class ColorLabelPreviewVO {

    /**
     * 预览HTML内容
     */
    private String previewHtml;

    /**
     * 预览CSS样式
     */
    private String previewCss;

    /**
     * 标签配置信息
     */
    private Map<String, Object> labelConfig;

    /**
     * 标签列表
     */
    private List<LabelItem> labelItems;

    /**
     * 打印配置
     */
    private PrintConfig printConfig;

    /**
     * 标签项
     */
    @Data
    public static class LabelItem {
        /**
         * 标签ID
         */
        private String labelId;

        /**
         * 标签内容
         */
        private String content;

        /**
         * 标签样式
         */
        private String style;

        /**
         * 标签HTML
         */
        private String html;
    }

    /**
     * 打印配置
     */
    @Data
    public static class PrintConfig {
        /**
         * 页面宽度（mm）
         */
        private Double pageWidth;

        /**
         * 页面高度（mm）
         */
        private Double pageHeight;

        /**
         * 页边距（mm）
         */
        private Double margin;

        /**
         * 每行标签数量
         */
        private Integer labelsPerRow;

        /**
         * 每页标签数量
         */
        private Integer labelsPerPage;

        /**
         * 标签间距（mm）
         */
        private Double labelSpacing;
    }
}
