# 彩色标签打印功能 - 项目规范调整说明

## 调整概述

根据项目规范要求，已对彩色标签打印功能的代码进行了调整，主要移除了Swagger相关注解，并按照项目编码规范重新组织代码。

## 主要调整内容

### 1. Controller层调整

**文件**: `ColorLabelController.java`

**调整内容**:
- 移除 `@Api` 和 `@ApiOperation` 注解
- 移除 `@ApiParam` 注解
- 添加 `@OperationLog` 注解用于操作日志记录
- 将 `@RequiredArgsConstructor` 改为 `@Resource` 注入方式
- 调整API路径为 `/api/banknote/color-label`

**调整前**:
```java
@Api(tags = "彩色标签打印")
@RestController
@RequestMapping("/api/color-label")
@RequiredArgsConstructor
public class ColorLabelController extends BaseController {
    private final ColorLabelService colorLabelService;
    
    @ApiOperation("获取预设模板列表")
    @GetMapping("/templates")
    public ApiResult<List<Map<String, Object>>> getPresetTemplates() {
        // ...
    }
}
```

**调整后**:
```java
@RestController
@RequestMapping("/api/banknote/color-label")
public class ColorLabelController extends BaseController {
    @Resource
    private ColorLabelService colorLabelService;
    
    @PreAuthorize("hasAuthority('banknote:color-label:view')")
    @OperationLog
    @GetMapping("/templates")
    public ApiResult<List<Map<String, Object>>> getPresetTemplates() {
        // ...
    }
}
```

### 2. DTO类调整

**文件**: `ColorLabelConfigDto.java`

**调整内容**:
- 移除 `@ApiModel` 注解
- 移除所有 `@ApiModelProperty` 注解
- 改用标准的JavaDoc注释

**调整前**:
```java
@Data
@ApiModel("彩色标签配置")
public class ColorLabelConfigDto {
    @ApiModelProperty("标签文本内容")
    @NotBlank(message = "标签文本不能为空")
    private String labelText;
}
```

**调整后**:
```java
@Data
public class ColorLabelConfigDto {
    /**
     * 标签文本内容
     */
    @NotBlank(message = "标签文本不能为空")
    private String labelText;
}
```

### 3. VO类调整

**文件**: `ColorLabelPreviewVO.java`

**调整内容**:
- 移除 `@ApiModel` 注解
- 移除所有 `@ApiModelProperty` 注解
- 改用标准的JavaDoc注释

### 4. 前端API调整

**文件**: `color-label-api.js`

**调整内容**:
- 更新API基础路径为 `/api/banknote/color-label`

**调整前**:
```javascript
const API_BASE = '/api/color-label';
```

**调整后**:
```javascript
const API_BASE = '/api/banknote/color-label';
```

## 权限配置

### 权限点定义
- `banknote:color-label:view` - 查看彩色标签功能
- `banknote:color-label:print` - 打印彩色标签
- `banknote:color-label:manage` - 管理自定义模板

### 权限应用
所有Controller方法都添加了相应的权限控制注解：

```java
@PreAuthorize("hasAuthority('banknote:color-label:view')")    // 查看权限
@PreAuthorize("hasAuthority('banknote:color-label:print')")   // 打印权限
@PreAuthorize("hasAuthority('banknote:color-label:manage')")  // 管理权限
```

## 操作日志

### 日志注解
为重要操作添加了操作日志记录：

```java
@OperationLog                                                    // 基础日志
@OperationLog(module = "彩色标签", comments = "保存自定义模板")      // 详细日志
@OperationLog(module = "彩色标签", comments = "删除自定义模板")      // 详细日志
```

## API接口更新

### 新的接口地址
所有API接口地址都已更新为新的路径格式：

| 功能 | 新接口地址 |
|------|-----------|
| 获取预设模板 | `GET /api/banknote/color-label/templates` |
| 生成预览数据 | `POST /api/banknote/color-label/preview` |
| 批量生成打印数据 | `POST /api/banknote/color-label/batch-generate` |
| 获取字体列表 | `GET /api/banknote/color-label/fonts` |
| 获取颜色预设 | `GET /api/banknote/color-label/colors` |
| 保存自定义模板 | `POST /api/banknote/color-label/template` |
| 获取自定义模板 | `GET /api/banknote/color-label/custom-templates` |
| 删除自定义模板 | `DELETE /api/banknote/color-label/template/{id}` |

## 代码规范遵循

### 1. 注解使用规范
- 使用 `@Resource` 进行依赖注入
- 使用 `@OperationLog` 记录操作日志
- 使用 `@PreAuthorize` 进行权限控制
- 移除所有Swagger相关注解

### 2. 注释规范
- 使用标准JavaDoc注释格式
- 为所有公共方法和字段添加注释
- 注释内容简洁明了

### 3. 包结构规范
- Controller放在 `controller` 包下
- Service接口放在 `service` 包下
- Service实现放在 `service.impl` 包下
- DTO放在 `entity.dto` 包下
- VO放在 `entity.vo` 包下

## 部署注意事项

### 1. 权限配置
需要在权限管理系统中添加以下权限点：
- `banknote:color-label:view`
- `banknote:color-label:print`
- `banknote:color-label:manage`

### 2. 前端配置
确保前端项目中的API请求地址已更新为新的路径格式。

### 3. 数据库配置
如果需要保存自定义模板功能，需要创建相应的数据库表结构。

## 测试建议

### 1. 功能测试
- 测试所有API接口的正常调用
- 测试权限控制是否生效
- 测试操作日志是否正常记录

### 2. 集成测试
- 测试前后端集成是否正常
- 测试打印功能是否正常工作
- 测试模板保存和加载功能

## 总结

本次调整完全按照项目规范要求进行，主要目标是：
1. 移除不需要的Swagger依赖
2. 统一代码注释风格
3. 规范权限控制和操作日志
4. 保持功能完整性

调整后的代码更符合项目规范，同时保持了原有功能的完整性和可扩展性。
