<template>
  <ele-page>
    <div class="color-label-page">
      <!-- 功能切换标签 -->
      <el-tabs v-model="activeTab" class="main-tabs">
        <!-- 传统模式 -->
        <el-tab-pane label="传统模式" name="traditional">
          <div class="main-content">
            <!-- 左侧控制面板 -->
            <div class="control-panel">
              <!-- 基础设置 -->
              <ele-card class="control-card" header="基础设置">
                <ColorLabelDesigner
                  ref="designerRef"
                  @preview="handlePreview"
                  @print="handlePrint"
                  @save-template="handleSaveTemplate"
                />
              </ele-card>

              <!-- 颜色预设面板 -->
              <ele-card class="control-card" header="颜色预设表">
                <ColorPresets @color-select="handleColorSelect" />
              </ele-card>

              <!-- 模板选择面板 -->
              <ele-card class="control-card" header="预设模板">
                <TemplateSelector @template-select="handleTemplateSelect" />
              </ele-card>
            </div>

            <!-- 右侧预览区域 -->
            <div class="preview-panel">
              <ele-card class="preview-card" header="标签预览">
                <LabelPreview
                  ref="previewRef"
                  :config="labelConfig"
                  :preview-data="previewData"
                />
              </ele-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- 一体化模式 -->
        <el-tab-pane label="一体化模式" name="integrated">
          <div class="integrated-content">
            <!-- 步骤指示器 -->
            <el-steps :active="currentStep" align-center class="steps-container">
              <el-step title="选择钱币" description="选择要打印的钱币" />
              <el-step title="设计标签" description="设计彩色标签样式" />
              <el-step title="定位预览" description="调整标签位置" />
              <el-step title="打印输出" description="生成打印文件" />
            </el-steps>

            <!-- 步骤内容 -->
            <div class="step-content">
              <!-- 步骤1：选择钱币 -->
              <div v-show="currentStep === 0" class="step-panel">
                <CoinSelector
                  ref="coinSelectorRef"
                  @selection-change="handleCoinSelection"
                  @coin-preview="handleCoinPreview"
                />
              </div>

              <!-- 步骤2：设计标签 -->
              <div v-show="currentStep === 1" class="step-panel">
                <div class="design-layout">
                  <div class="design-left">
                    <ele-card header="标签设计">
                      <ColorLabelDesigner
                        ref="integratedDesignerRef"
                        @preview="handleIntegratedPreview"
                        @print="handleIntegratedPrint"
                        @save-template="handleSaveTemplate"
                      />
                    </ele-card>
                  </div>
                  <div class="design-right">
                    <ele-card header="快速设置">
                      <ColorPresets @color-select="handleColorSelect" />
                      <TemplateSelector @template-select="handleTemplateSelect" />
                    </ele-card>
                  </div>
                </div>
              </div>

              <!-- 步骤3：定位预览 -->
              <div v-show="currentStep === 2" class="step-panel">
                <CoinLabelPreview
                  ref="coinPreviewRef"
                  :color-label-config="labelConfig"
                  :coin-data="selectedCoinData"
                  @position-change="handlePositionChange"
                  @label-select="handleLabelSelect"
                />
              </div>

              <!-- 步骤4：打印输出 -->
              <div v-show="currentStep === 3" class="step-panel">
                <div class="print-summary">
                  <ele-card header="打印摘要">
                    <div class="summary-info">
                      <div class="summary-item">
                        <span class="label">选择钱币：</span>
                        <span class="value">{{ selectedCoins.length }} 个</span>
                      </div>
                      <div class="summary-item">
                        <span class="label">标签文本：</span>
                        <span class="value">{{ labelConfig.labelText }}</span>
                      </div>
                      <div class="summary-item">
                        <span class="label">标签位置：</span>
                        <span class="value">X:{{ labelPosition.x }}, Y:{{ labelPosition.y }}</span>
                      </div>
                      <div class="summary-item">
                        <span class="label">标签尺寸：</span>
                        <span class="value">{{ labelPosition.width }}×{{ labelPosition.height }}</span>
                      </div>
                    </div>
                    <div class="print-actions">
                      <el-button type="primary" size="large" @click="generateIntegratedPrint">
                        <el-icon><Printer /></el-icon>
                        生成一体化打印
                      </el-button>
                      <el-button size="large" @click="previewIntegratedPrint">
                        <el-icon><View /></el-icon>
                        预览打印效果
                      </el-button>
                    </div>
                  </ele-card>
                </div>
              </div>
            </div>

            <!-- 步骤导航 -->
            <div class="step-navigation">
              <el-button
                :disabled="currentStep === 0"
                @click="prevStep"
              >
                上一步
              </el-button>
              <el-button
                type="primary"
                :disabled="!canNextStep"
                @click="nextStep"
              >
                {{ currentStep === 3 ? '完成' : '下一步' }}
              </el-button>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 打印预览对话框 -->
      <PrintPreview
        v-model="showPrintPreview"
        :print-data="printData"
        @confirm-print="handleConfirmPrint"
      />

      <!-- 保存模板对话框 -->
      <SaveTemplateDialog
        v-model="showSaveDialog"
        :config="labelConfig"
        @save-success="handleSaveSuccess"
      />
    </div>
  </ele-page>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';

// 组件导入
import ColorLabelDesigner from './components/ColorLabelDesigner.vue';
import ColorPresets from './components/ColorPresets.vue';
import TemplateSelector from './components/TemplateSelector.vue';
import LabelPreview from './components/LabelPreview.vue';
import PrintPreview from './components/PrintPreview.vue';
import SaveTemplateDialog from './components/SaveTemplateDialog.vue';
import CoinLabelPreview from './components/CoinLabelPreview.vue';
import CoinSelector from './components/CoinSelector.vue';

// API导入
import { colorLabelApi } from './api/enhanced-index.js';

// 响应式数据
const activeTab = ref('traditional');
const currentStep = ref(0);
const designerRef = ref(null);
const integratedDesignerRef = ref(null);
const previewRef = ref(null);
const coinSelectorRef = ref(null);
const coinPreviewRef = ref(null);
const showPrintPreview = ref(false);
const showSaveDialog = ref(false);

// 一体化模式数据
const selectedCoins = ref([]);
const selectedCoinData = ref(null);
const labelPosition = reactive({
  x: 120,
  y: 25,
  width: 60,
  height: 20
});

// 标签配置
const labelConfig = reactive({
  labelText: '',
  textColor: '#0000FF',
  backgroundColor: '',
  fontFamily: '青鸟华光简行楷',
  fontSize: 40,
  fontWeight: 'normal',
  fontStyle: 'normal',
  textAlign: 'center',
  lineHeight: 1.2,
  letterSpacing: 0,
  labelCount: 10,
  templateName: '',
  usePresetTemplate: false,
  customCss: '',
  enableShadow: false,
  shadowConfig: null,
  enableBorder: false,
  borderConfig: null,
  enableGradient: false,
  gradientConfig: null
});

// 预览数据
const previewData = ref(null);
const printData = ref(null);

// 计算属性
const canNextStep = computed(() => {
  switch (currentStep.value) {
    case 0: // 选择钱币
      return selectedCoins.value.length > 0;
    case 1: // 设计标签
      return labelConfig.labelText && labelConfig.labelText.trim() !== '';
    case 2: // 定位预览
      return true; // 位置可以随时调整
    case 3: // 打印输出
      return true;
    default:
      return false;
  }
});

// 生命周期
onMounted(() => {
  initializeData();
});

// 方法
const initializeData = async () => {
  try {
    // 初始化默认配置
    await nextTick();

    // 生成初始预览
    if (labelConfig.labelText) {
      await generatePreview();
    }
  } catch (error) {
    console.error('初始化失败:', error);
  }
};

const handlePreview = async (config) => {
  try {
    Object.assign(labelConfig, config);
    await generatePreview();
  } catch (error) {
    EleMessage.error('生成预览失败：' + error.message);
  }
};

const handlePrint = async (config) => {
  try {
    Object.assign(labelConfig, config);

    // 生成打印数据
    const data = await colorLabelApi.batchGenerate(labelConfig);
    printData.value = data;
    showPrintPreview.value = true;
  } catch (error) {
    EleMessage.error('生成打印数据失败：' + error.message);
  }
};

const handleSaveTemplate = (config) => {
  Object.assign(labelConfig, config);
  showSaveDialog.value = true;
};

const handleColorSelect = (color) => {
  labelConfig.textColor = color;
  if (designerRef.value) {
    designerRef.value.updateColor(color);
  }
  generatePreview();
};

const handleTemplateSelect = async (template) => {
  try {
    // 应用模板配置
    Object.assign(labelConfig, {
      labelText: template.content?.replace(/<[^>]*>/g, '') || template.name,
      textColor: template.color || '#0000FF',
      fontFamily: template.fontFamily || '宋体',
      fontSize: template.fontSize || 32,
      templateName: template.name,
      usePresetTemplate: true
    });

    // 更新设计器
    if (designerRef.value) {
      designerRef.value.applyTemplate(template);
    }

    // 生成预览
    await generatePreview();

    EleMessage.success(`已应用模板：${template.name}`);
  } catch (error) {
    EleMessage.error('应用模板失败：' + error.message);
  }
};

const generatePreview = async () => {
  try {
    if (!labelConfig.labelText.trim()) {
      previewData.value = null;
      return;
    }

    const preview = await colorLabelApi.generatePreview(labelConfig);
    previewData.value = preview;

    // 更新预览组件
    if (previewRef.value) {
      previewRef.value.updatePreview(preview);
    }
  } catch (error) {
    console.error('生成预览失败:', error);
    previewData.value = null;
  }
};

const handleConfirmPrint = async (printOptions) => {
  try {
    // 创建打印窗口
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      throw new Error('无法打开打印窗口，请检查浏览器弹窗设置');
    }

    // 写入打印内容
    printWindow.document.write(printData.value.html);
    printWindow.document.close();

    // 等待内容加载完成后打印
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 500);
    };

    showPrintPreview.value = false;
    EleMessage.success('打印任务已发送');
  } catch (error) {
    EleMessage.error('打印失败：' + error.message);
  }
};

const handleSaveSuccess = () => {
  showSaveDialog.value = false;
  EleMessage.success('模板保存成功');
};

// ========== 一体化模式方法 ==========

const nextStep = () => {
  if (currentStep.value < 3) {
    currentStep.value++;

    // 步骤切换时的特殊处理
    if (currentStep.value === 2 && selectedCoins.value.length > 0) {
      // 进入定位预览步骤时，设置第一个钱币作为预览数据
      selectedCoinData.value = selectedCoins.value[0];
    }
  }
};

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
};

const handleCoinSelection = (coins) => {
  selectedCoins.value = coins;
  if (coins.length > 0) {
    selectedCoinData.value = coins[0]; // 使用第一个钱币作为预览
  }
};

const handleCoinPreview = (coin) => {
  selectedCoinData.value = coin;
};

const handleIntegratedPreview = async (config) => {
  try {
    Object.assign(labelConfig, config);

    if (selectedCoinData.value) {
      // 生成带位置信息的预览
      const previewConfig = {
        ...config,
        position: labelPosition,
        coinData: selectedCoinData.value
      };

      const preview = await colorLabelApi.generatePositionPreview(previewConfig);
      previewData.value = preview;

      // 更新钱币预览组件
      if (coinPreviewRef.value) {
        coinPreviewRef.value.updatePreview(preview);
      }
    }
  } catch (error) {
    EleMessage.error('生成预览失败：' + error.message);
  }
};

const handleIntegratedPrint = async (config) => {
  try {
    Object.assign(labelConfig, config);

    if (selectedCoins.value.length === 0) {
      EleMessage.warning('请先选择要打印的钱币');
      return;
    }

    // 跳转到打印步骤
    currentStep.value = 3;
  } catch (error) {
    EleMessage.error('准备打印失败：' + error.message);
  }
};

const handlePositionChange = (position) => {
  Object.assign(labelPosition, position);

  // 实时更新预览
  if (labelConfig.labelText) {
    handleIntegratedPreview(labelConfig);
  }
};

const handleLabelSelect = () => {
  // 标签被选中时的处理
  console.log('标签已选中');
};

const generateIntegratedPrint = async () => {
  try {
    if (selectedCoins.value.length === 0) {
      EleMessage.warning('请先选择要打印的钱币');
      return;
    }

    if (!labelConfig.labelText || labelConfig.labelText.trim() === '') {
      EleMessage.warning('请先设计彩色标签');
      return;
    }

    // 生成一体化打印数据
    const printConfig = {
      labelConfig: { ...labelConfig },
      position: { ...labelPosition },
      coinDataList: selectedCoins.value
    };

    const printResult = await colorLabelApi.generateIntegratedPrint(printConfig);

    // 创建打印窗口
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      throw new Error('无法打开打印窗口，请检查浏览器弹窗设置');
    }

    // 写入打印内容
    printWindow.document.write(printResult.html);
    printWindow.document.close();

    // 等待内容加载完成后打印
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print();
      }, 500);
    };

    EleMessage.success('一体化打印任务已发送');
  } catch (error) {
    EleMessage.error('生成打印失败：' + error.message);
  }
};

const previewIntegratedPrint = async () => {
  try {
    if (selectedCoins.value.length === 0) {
      EleMessage.warning('请先选择要打印的钱币');
      return;
    }

    // 生成预览数据
    const printConfig = {
      labelConfig: { ...labelConfig },
      position: { ...labelPosition },
      coinDataList: selectedCoins.value.slice(0, 5) // 只预览前5个
    };

    const printResult = await colorLabelApi.generateIntegratedPrint(printConfig);
    printData.value = printResult;
    showPrintPreview.value = true;
  } catch (error) {
    EleMessage.error('生成预览失败：' + error.message);
  }
};

// 暴露方法给父组件
defineExpose({
  generatePreview,
  getLabelConfig: () => labelConfig,
  setLabelConfig: (config) => Object.assign(labelConfig, config)
});
</script>

<style scoped>
.color-label-page {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.main-content {
  display: flex;
  gap: 20px;
  min-height: calc(100vh - 200px);
}

.control-panel {
  width: 380px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.preview-panel {
  flex: 1;
  min-width: 0;
}

.control-card,
.preview-card {
  height: fit-content;
}

.control-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-card {
  min-height: 500px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 一体化模式样式 */
.main-tabs {
  height: calc(100vh - 200px);
}

.main-tabs :deep(.el-tab-pane) {
  height: 100%;
}

.integrated-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.steps-container {
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.step-content {
  flex: 1;
  min-height: 0;
  margin-bottom: 20px;
}

.step-panel {
  height: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.design-layout {
  display: flex;
  height: 100%;
  gap: 20px;
  padding: 20px;
}

.design-left {
  flex: 2;
  min-width: 0;
}

.design-right {
  flex: 1;
  min-width: 300px;
}

.print-summary {
  padding: 20px;
  height: 100%;
}

.summary-info {
  margin-bottom: 24px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-item .label {
  font-weight: 500;
  color: #606266;
}

.summary-item .value {
  color: #303133;
  font-weight: 600;
}

.print-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.step-navigation {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .control-panel {
    width: 100%;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .control-card {
    flex: 1;
    min-width: 300px;
  }

  .design-layout {
    flex-direction: column;
  }

  .design-right {
    min-width: 0;
  }
}

@media (max-width: 768px) {
  .control-panel {
    flex-direction: column;
  }

  .control-card {
    width: 100%;
  }

  .steps-container {
    padding: 15px;
  }

  .design-layout {
    padding: 15px;
  }

  .print-actions {
    flex-direction: column;
  }

  .step-navigation {
    padding: 15px;
  }
}
</style>
