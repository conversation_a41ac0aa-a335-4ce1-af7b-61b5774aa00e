<template>
  <ele-page>
    <div class="color-label-page">
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 左侧控制面板 -->
        <div class="control-panel">
          <!-- 基础设置 -->
          <ele-card class="control-card" header="基础设置">
            <ColorLabelDesigner
              ref="designerRef"
              @preview="handlePreview"
              @print="handlePrint"
              @save-template="handleSaveTemplate"
            />
          </ele-card>

          <!-- 颜色预设面板 -->
          <ele-card class="control-card" header="颜色预设表">
            <ColorPresets @color-select="handleColorSelect" />
          </ele-card>

          <!-- 模板选择面板 -->
          <ele-card class="control-card" header="预设模板">
            <TemplateSelector @template-select="handleTemplateSelect" />
          </ele-card>
        </div>

        <!-- 右侧预览区域 -->
        <div class="preview-panel">
          <ele-card class="preview-card" header="标签预览">
            <LabelPreview
              ref="previewRef"
              :config="labelConfig"
              :preview-data="previewData"
            />
          </ele-card>
        </div>
      </div>

      <!-- 打印预览对话框 -->
      <PrintPreview
        v-model="showPrintPreview"
        :print-data="printData"
        @confirm-print="handleConfirmPrint"
      />

      <!-- 保存模板对话框 -->
      <SaveTemplateDialog
        v-model="showSaveDialog"
        :config="labelConfig"
        @save-success="handleSaveSuccess"
      />
    </div>
  </ele-page>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';

// 组件导入
import ColorLabelDesigner from './components/ColorLabelDesigner.vue';
import ColorPresets from './components/ColorPresets.vue';
import TemplateSelector from './components/TemplateSelector.vue';
import LabelPreview from './components/LabelPreview.vue';
import PrintPreview from './components/PrintPreview.vue';
import SaveTemplateDialog from './components/SaveTemplateDialog.vue';

// API导入
import { colorLabelApi } from './api';

// 响应式数据
const designerRef = ref(null);
const previewRef = ref(null);
const showPrintPreview = ref(false);
const showSaveDialog = ref(false);

// 标签配置
const labelConfig = reactive({
  labelText: '',
  textColor: '#0000FF',
  backgroundColor: '',
  fontFamily: '青鸟华光简行楷',
  fontSize: 40,
  fontWeight: 'normal',
  fontStyle: 'normal',
  textAlign: 'center',
  lineHeight: 1.2,
  letterSpacing: 0,
  labelCount: 10,
  templateName: '',
  usePresetTemplate: false,
  customCss: '',
  enableShadow: false,
  shadowConfig: null,
  enableBorder: false,
  borderConfig: null,
  enableGradient: false,
  gradientConfig: null
});

// 预览数据
const previewData = ref(null);
const printData = ref(null);

// 生命周期
onMounted(() => {
  initializeData();
});

// 方法
const initializeData = async () => {
  try {
    // 初始化默认配置
    await nextTick();

    // 生成初始预览
    if (labelConfig.labelText) {
      await generatePreview();
    }
  } catch (error) {
    console.error('初始化失败:', error);
  }
};

const handlePreview = async (config) => {
  try {
    Object.assign(labelConfig, config);
    await generatePreview();
  } catch (error) {
    EleMessage.error('生成预览失败：' + error.message);
  }
};

const handlePrint = async (config) => {
  try {
    Object.assign(labelConfig, config);

    // 生成打印数据
    const data = await colorLabelApi.batchGenerate(labelConfig);
    printData.value = data;
    showPrintPreview.value = true;
  } catch (error) {
    EleMessage.error('生成打印数据失败：' + error.message);
  }
};

const handleSaveTemplate = (config) => {
  Object.assign(labelConfig, config);
  showSaveDialog.value = true;
};

const handleColorSelect = (color) => {
  labelConfig.textColor = color;
  if (designerRef.value) {
    designerRef.value.updateColor(color);
  }
  generatePreview();
};

const handleTemplateSelect = async (template) => {
  try {
    // 应用模板配置
    Object.assign(labelConfig, {
      labelText: template.content?.replace(/<[^>]*>/g, '') || template.name,
      textColor: template.color || '#0000FF',
      fontFamily: template.fontFamily || '宋体',
      fontSize: template.fontSize || 32,
      templateName: template.name,
      usePresetTemplate: true
    });

    // 更新设计器
    if (designerRef.value) {
      designerRef.value.applyTemplate(template);
    }

    // 生成预览
    await generatePreview();

    EleMessage.success(`已应用模板：${template.name}`);
  } catch (error) {
    EleMessage.error('应用模板失败：' + error.message);
  }
};

const generatePreview = async () => {
  try {
    if (!labelConfig.labelText.trim()) {
      previewData.value = null;
      return;
    }

    const preview = await colorLabelApi.generatePreview(labelConfig);
    previewData.value = preview;

    // 更新预览组件
    if (previewRef.value) {
      previewRef.value.updatePreview(preview);
    }
  } catch (error) {
    console.error('生成预览失败:', error);
    previewData.value = null;
  }
};

const handleConfirmPrint = async (printOptions) => {
  try {
    // 创建打印窗口
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      throw new Error('无法打开打印窗口，请检查浏览器弹窗设置');
    }

    // 写入打印内容
    printWindow.document.write(printData.value.html);
    printWindow.document.close();

    // 等待内容加载完成后打印
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 500);
    };

    showPrintPreview.value = false;
    EleMessage.success('打印任务已发送');
  } catch (error) {
    EleMessage.error('打印失败：' + error.message);
  }
};

const handleSaveSuccess = () => {
  showSaveDialog.value = false;
  EleMessage.success('模板保存成功');
};

// 暴露方法给父组件
defineExpose({
  generatePreview,
  getLabelConfig: () => labelConfig,
  setLabelConfig: (config) => Object.assign(labelConfig, config)
});
</script>

<style scoped>
.color-label-page {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.main-content {
  display: flex;
  gap: 20px;
  min-height: calc(100vh - 200px);
}

.control-panel {
  width: 380px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.preview-panel {
  flex: 1;
  min-width: 0;
}

.control-card,
.preview-card {
  height: fit-content;
}

.control-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-card {
  min-height: 500px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .control-panel {
    width: 100%;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .control-card {
    flex: 1;
    min-width: 300px;
  }
}

@media (max-width: 768px) {
  .control-panel {
    flex-direction: column;
  }

  .control-card {
    width: 100%;
  }
}
</style>
