/**
 * 彩色标签打印API - 增强版
 * 
 * <AUTHOR>
 * @since 2025-08-03
 */
import request from '@/utils/request';

const API_BASE = '/api/banknote/color-label';

/**
 * 彩色标签API - 增强版
 */
export const colorLabelApi = {
  /**
   * 获取预设模板列表
   */
  async getPresetTemplates() {
    const res = await request.get(`${API_BASE}/templates`);
    return res.data;
  },

  /**
   * 生成彩色标签预览数据
   * @param {Object} config 标签配置
   */
  async generatePreview(config) {
    const res = await request.post(`${API_BASE}/preview`, config);
    return res.data;
  },

  /**
   * 批量生成彩色标签打印数据
   * @param {Object} config 标签配置
   */
  async batchGenerate(config) {
    const res = await request.post(`${API_BASE}/batch-generate`, config);
    return res.data;
  },

  /**
   * 获取字体列表
   */
  async getFontList() {
    const res = await request.get(`${API_BASE}/fonts`);
    return res.data;
  },

  /**
   * 获取颜色预设
   */
  async getColorPresets() {
    const res = await request.get(`${API_BASE}/colors`);
    return res.data;
  },

  /**
   * 保存用户自定义模板
   * @param {Object} config 模板配置
   */
  async saveCustomTemplate(config) {
    const res = await request.post(`${API_BASE}/template`, config);
    return res.data;
  },

  /**
   * 获取用户自定义模板列表
   */
  async getCustomTemplates() {
    const res = await request.get(`${API_BASE}/custom-templates`);
    return res.data;
  },

  /**
   * 删除用户自定义模板
   * @param {string} id 模板ID
   */
  async deleteCustomTemplate(id) {
    const res = await request.delete(`${API_BASE}/template/${id}`);
    return res.data;
  },

  // ========== 新增的增强功能 ==========

  /**
   * 获取钱币数据（用于标签预览）
   * @param {string} coinId 钱币ID
   */
  async getCoinData(coinId) {
    const res = await request.get(`${API_BASE}/coin/${coinId}`);
    return res.data;
  },

  /**
   * 批量获取钱币数据
   * @param {Array} coinIds 钱币ID列表
   */
  async getBatchCoinData(coinIds) {
    const res = await request.post(`${API_BASE}/coins/batch`, { coinIds });
    return res.data;
  },

  /**
   * 生成带位置信息的预览
   * @param {Object} config 标签配置（包含位置信息）
   */
  async generatePositionPreview(config) {
    const res = await request.post(`${API_BASE}/position-preview`, config);
    return res.data;
  },

  /**
   * 生成一体化打印数据（钱币标签+彩色标签）
   * @param {Object} config 完整配置
   */
  async generateIntegratedPrint(config) {
    const res = await request.post(`${API_BASE}/integrated-print`, config);
    return res.data;
  },

  /**
   * 保存标签位置配置
   * @param {Object} positionConfig 位置配置
   */
  async savePositionConfig(positionConfig) {
    const res = await request.post(`${API_BASE}/position-config`, positionConfig);
    return res.data;
  },

  /**
   * 获取标签位置配置
   * @param {string} templateId 模板ID
   */
  async getPositionConfig(templateId) {
    const res = await request.get(`${API_BASE}/position-config/${templateId}`);
    return res.data;
  },

  /**
   * 获取钱币标签模板列表
   */
  async getCoinLabelTemplates() {
    const res = await request.get(`${API_BASE}/coin-templates`);
    return res.data;
  },

  /**
   * 验证标签位置是否合适
   * @param {Object} positionData 位置数据
   */
  async validateLabelPosition(positionData) {
    const res = await request.post(`${API_BASE}/validate-position`, positionData);
    return res.data;
  }
};

/**
 * 彩色标签工具类 - 增强版
 */
export class ColorLabelUtils {
  /**
   * 智能字体大小调整
   * @param {string} text 文本内容
   * @returns {number} 推荐字体大小
   */
  static getRecommendedFontSize(text) {
    const length = text.length;
    if (length <= 4) return 40;
    if (length === 5) return 32;
    if (length >= 6) return 27;
    return 24;
  }

  /**
   * 生成标签样式
   * @param {Object} config 标签配置
   * @returns {string} CSS样式字符串
   */
  static generateLabelStyle(config) {
    const styles = [];
    
    if (config.textColor) {
      styles.push(`color: ${config.textColor}`);
    }
    
    if (config.fontFamily) {
      styles.push(`font-family: ${config.fontFamily}`);
    }
    
    if (config.fontSize) {
      styles.push(`font-size: ${config.fontSize}px`);
    }
    
    if (config.fontWeight) {
      styles.push(`font-weight: ${config.fontWeight}`);
    }
    
    if (config.fontStyle) {
      styles.push(`font-style: ${config.fontStyle}`);
    }
    
    if (config.textAlign) {
      styles.push(`text-align: ${config.textAlign}`);
    }
    
    if (config.lineHeight) {
      styles.push(`line-height: ${config.lineHeight}`);
    }
    
    if (config.letterSpacing) {
      styles.push(`letter-spacing: ${config.letterSpacing}px`);
    }
    
    if (config.backgroundColor) {
      styles.push(`background-color: ${config.backgroundColor}`);
    }
    
    // 阴影效果
    if (config.enableShadow && config.shadowConfig) {
      const shadow = config.shadowConfig;
      styles.push(`text-shadow: ${shadow.offsetX}px ${shadow.offsetY}px ${shadow.blurRadius}px ${shadow.shadowColor}`);
    }
    
    // 边框效果
    if (config.enableBorder && config.borderConfig) {
      const border = config.borderConfig;
      styles.push(`border: ${border.borderWidth}px ${border.borderStyle} ${border.borderColor}`);
      if (border.borderRadius) {
        styles.push(`border-radius: ${border.borderRadius}px`);
      }
    }
    
    return styles.join('; ');
  }

  /**
   * 生成一体化打印CSS
   * @param {Object} config 标签配置
   * @param {Object} position 位置信息
   * @returns {string} 打印CSS
   */
  static generateIntegratedPrintCSS(config, position) {
    return `
      @page {
        size: A4;
        margin: 10mm;
      }
      
      body {
        margin: 0;
        padding: 0;
        font-family: Arial, sans-serif;
      }
      
      .print-container {
        display: flex;
        flex-direction: column;
        gap: 2mm;
      }
      
      .coin-label-row {
        display: flex;
        flex-wrap: wrap;
        gap: 2mm;
      }
      
      .coin-label-item {
        width: 200px;
        height: 60px;
        border: 1px solid #000;
        position: relative;
        background: white;
        page-break-inside: avoid;
        display: flex;
        padding: 8px;
        font-size: 10px;
        line-height: 1.2;
      }
      
      .coin-info-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
      
      .score-section {
        display: flex;
        align-items: center;
        margin: 0 10px;
      }
      
      .score-number {
        font-size: 24px;
        font-weight: bold;
        margin-right: 8px;
      }
      
      .qr-code-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      
      .color-label-overlay {
        position: absolute;
        left: ${position.x}px;
        top: ${position.y}px;
        width: ${position.width}px;
        height: ${position.height}px;
        ${this.generateLabelStyle(config)}
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        z-index: 10;
      }
      
      @media print {
        .no-print {
          display: none !important;
        }
      }
    `;
  }

  /**
   * 生成一体化打印HTML
   * @param {Object} config 标签配置
   * @param {Object} position 位置信息
   * @param {Array} coinDataList 钱币数据列表
   * @returns {string} 打印HTML
   */
  static generateIntegratedPrintHTML(config, position, coinDataList) {
    const coinLabels = coinDataList.map((coinData, index) => `
      <div class="coin-label-item">
        <div class="coin-info-section">
          <div class="coin-title">${coinData.bankName || '中国人民银行'}</div>
          <div class="coin-year">${coinData.year || '1980年'} ${coinData.denomination || '贰角'}</div>
          <div class="coin-serial">S/N ${coinData.serialNumber || 'FZ-********'} - ${coinData.description || '民族人物头像'}</div>
        </div>
        <div class="score-section">
          <div class="score-number">${coinData.score || '68'}</div>
          <div class="score-text">
            <div>${coinData.gradeType || 'EPQ'}</div>
            <div>${coinData.gradeDescription || 'Superb Gem Unc'}</div>
          </div>
        </div>
        <div class="qr-code-section">
          <div class="qr-code-placeholder"></div>
          <div class="qr-code-text">${coinData.qrCode || 'ZK25080001'}</div>
        </div>
        <div class="color-label-overlay">
          ${config.labelText}
        </div>
      </div>
    `).join('');

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>钱币标签一体化打印</title>
        <style>
          ${this.generateIntegratedPrintCSS(config, position)}
        </style>
      </head>
      <body>
        <div class="print-container">
          <div class="coin-label-row">
            ${coinLabels}
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * 验证位置是否在合适区域
   * @param {Object} position 位置信息
   * @param {Object} templateSize 模板尺寸
   * @returns {Object} 验证结果
   */
  static validatePosition(position, templateSize = { width: 200, height: 60 }) {
    const warnings = [];
    const suggestions = [];
    
    // 检查是否超出边界
    if (position.x + position.width > templateSize.width) {
      warnings.push('标签超出右边界');
      suggestions.push('减小标签宽度或向左移动');
    }
    
    if (position.y + position.height > templateSize.height) {
      warnings.push('标签超出下边界');
      suggestions.push('减小标签高度或向上移动');
    }
    
    // 检查是否在推荐区域
    const recommendedAreas = [
      { name: '信息与评分间', x: 100, y: 20, width: 40, height: 20 },
      { name: '序列号下方', x: 20, y: 40, width: 80, height: 15 },
      { name: '评分左侧', x: 80, y: 10, width: 30, height: 25 }
    ];
    
    let inRecommendedArea = false;
    for (const area of recommendedAreas) {
      if (position.x >= area.x && position.y >= area.y &&
          position.x + position.width <= area.x + area.width &&
          position.y + position.height <= area.y + area.height) {
        inRecommendedArea = true;
        suggestions.push(`当前位置在推荐区域：${area.name}`);
        break;
      }
    }
    
    if (!inRecommendedArea) {
      suggestions.push('建议将标签放置在信息与评分之间的空白区域');
    }
    
    return {
      isValid: warnings.length === 0,
      warnings,
      suggestions,
      inRecommendedArea
    };
  }

  /**
   * 获取推荐位置
   * @param {string} labelText 标签文本
   * @returns {Object} 推荐位置
   */
  static getRecommendedPosition(labelText) {
    const textLength = labelText.length;
    
    // 根据文本长度推荐不同位置
    if (textLength <= 4) {
      return { x: 120, y: 25, width: 60, height: 20 }; // 信息与评分间
    } else if (textLength <= 8) {
      return { x: 20, y: 45, width: 100, height: 15 }; // 序列号下方
    } else {
      return { x: 90, y: 15, width: 80, height: 25 }; // 评分左侧
    }
  }

  /**
   * 计算最佳字体大小
   * @param {string} text 文本内容
   * @param {Object} containerSize 容器尺寸
   * @returns {number} 最佳字体大小
   */
  static calculateOptimalFontSize(text, containerSize) {
    const textLength = text.length;
    const containerArea = containerSize.width * containerSize.height;
    
    // 基于容器面积和文本长度计算
    let fontSize = Math.sqrt(containerArea / textLength) * 0.8;
    
    // 限制范围
    fontSize = Math.max(8, Math.min(fontSize, 24));
    
    return Math.round(fontSize);
  }
}

export default colorLabelApi;
