import request from '@/utils/request';

const API_BASE = '/banknote/color-label';

/**
 * 彩色标签API
 */
export const colorLabelApi = {
  /**
   * 获取预设模板列表
   */
  async getPresetTemplates() {
    const res = await request.get(`${API_BASE}/templates`);
    return res.data;
  },

  /**
   * 生成彩色标签预览数据
   * @param {Object} config 标签配置
   */
  async generatePreview(config) {
    const res = await request.post(`${API_BASE}/preview`, config);
    return res.data;
  },

  /**
   * 批量生成彩色标签打印数据
   * @param {Object} config 标签配置
   */
  async batchGenerate(config) {
    const res = await request.post(`${API_BASE}/batch-generate`, config);
    return res.data;
  },

  /**
   * 获取字体列表
   */
  async getFontList() {
    const res = await request.get(`${API_BASE}/fonts`);
    return res.data;
  },

  /**
   * 获取颜色预设
   */
  async getColorPresets() {
    const res = await request.get(`${API_BASE}/colors`);
    return res.data;
  },

  /**
   * 保存用户自定义模板
   * @param {Object} config 模板配置
   */
  async saveCustomTemplate(config) {
    const res = await request.post(`${API_BASE}/template`, config);
    return res.data;
  },

  /**
   * 获取用户自定义模板列表
   */
  async getCustomTemplates() {
    const res = await request.get(`${API_BASE}/custom-templates`);
    return res.data;
  },

  /**
   * 删除用户自定义模板
   * @param {string} id 模板ID
   */
  async deleteCustomTemplate(id) {
    const res = await request.delete(`${API_BASE}/template/${id}`);
    return res.data;
  }
};

/**
 * 彩色标签工具类
 */
export class ColorLabelUtils {
  /**
   * 智能字体大小调整
   * @param {string} text 文本内容
   * @returns {number} 推荐字体大小
   */
  static getRecommendedFontSize(text) {
    const length = text.length;
    if (length <= 4) return 40;
    if (length === 5) return 32;
    if (length >= 6) return 27;
    return 24;
  }

  /**
   * 生成标签样式
   * @param {Object} config 标签配置
   * @returns {string} CSS样式字符串
   */
  static generateLabelStyle(config) {
    const styles = [];

    if (config.textColor) {
      styles.push(`color: ${config.textColor}`);
    }

    if (config.fontFamily) {
      styles.push(`font-family: ${config.fontFamily}`);
    }

    if (config.fontSize) {
      styles.push(`font-size: ${config.fontSize}px`);
    }

    if (config.fontWeight) {
      styles.push(`font-weight: ${config.fontWeight}`);
    }

    if (config.fontStyle) {
      styles.push(`font-style: ${config.fontStyle}`);
    }

    if (config.textAlign) {
      styles.push(`text-align: ${config.textAlign}`);
    }

    if (config.lineHeight) {
      styles.push(`line-height: ${config.lineHeight}`);
    }

    if (config.letterSpacing) {
      styles.push(`letter-spacing: ${config.letterSpacing}px`);
    }

    if (config.backgroundColor) {
      styles.push(`background-color: ${config.backgroundColor}`);
    }

    // 阴影效果
    if (config.enableShadow && config.shadowConfig) {
      const shadow = config.shadowConfig;
      styles.push(`text-shadow: ${shadow.offsetX}px ${shadow.offsetY}px ${shadow.blurRadius}px ${shadow.shadowColor}`);
    }

    // 边框效果
    if (config.enableBorder && config.borderConfig) {
      const border = config.borderConfig;
      styles.push(`border: ${border.borderWidth}px ${border.borderStyle} ${border.borderColor}`);
      if (border.borderRadius) {
        styles.push(`border-radius: ${border.borderRadius}px`);
      }
    }

    // 渐变色效果
    if (config.enableGradient && config.gradientConfig) {
      const gradient = config.gradientConfig;
      if (gradient.gradientType === 'linear') {
        const colorStops = gradient.colorStops
          .map(stop => `${stop.color} ${stop.position}%`)
          .join(', ');
        styles.push(`background: linear-gradient(${gradient.gradientDirection}, ${colorStops})`);
      } else if (gradient.gradientType === 'radial') {
        const colorStops = gradient.colorStops
          .map(stop => `${stop.color} ${stop.position}%`)
          .join(', ');
        styles.push(`background: radial-gradient(circle, ${colorStops})`);
      }
    }

    return styles.join('; ');
  }

  /**
   * 生成打印CSS
   * @param {Object} config 标签配置
   * @returns {string} 打印CSS
   */
  static generatePrintCSS(config) {
    return `
      @page {
        size: A4;
        margin: 10mm;
      }

      body {
        margin: 0;
        padding: 0;
        font-family: Arial, sans-serif;
      }

      .print-container {
        display: flex;
        flex-wrap: wrap;
        gap: 5mm;
      }

      .print-label {
        ${this.generateLabelStyle(config)}
        padding: 3mm;
        border: 1px solid #ccc;
        width: 40mm;
        height: 20mm;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        page-break-inside: avoid;
      }

      @media print {
        .no-print {
          display: none !important;
        }
      }
    `;
  }

  /**
   * 生成打印HTML
   * @param {Object} config 标签配置
   * @returns {string} 打印HTML
   */
  static generatePrintHTML(config) {
    const labels = Array.from({ length: config.labelCount }, (_, i) =>
      `<div class="print-label">${config.labelText}</div>`
    ).join('');

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>彩色标签批量打印</title>
        <style>
          ${this.generatePrintCSS(config)}
        </style>
      </head>
      <body>
        <div class="print-container">
          ${labels}
        </div>
      </body>
      </html>
    `;
  }

  /**
   * 颜色格式转换
   * @param {string} color 颜色值
   * @param {string} format 目标格式 (hex, rgb, hsl)
   * @returns {string} 转换后的颜色值
   */
  static convertColor(color, format) {
    // 简单的颜色转换实现
    // 实际项目中可以使用更完善的颜色处理库
    if (format === 'hex' && color.startsWith('rgb')) {
      // RGB to HEX conversion
      const rgb = color.match(/\d+/g);
      if (rgb && rgb.length >= 3) {
        const hex = rgb.slice(0, 3)
          .map(x => parseInt(x).toString(16).padStart(2, '0'))
          .join('');
        return `#${hex}`;
      }
    }
    return color;
  }

  /**
   * 验证配置
   * @param {Object} config 标签配置
   * @returns {Object} 验证结果
   */
  static validateConfig(config) {
    const errors = [];

    if (!config.labelText || config.labelText.trim() === '') {
      errors.push('标签文本不能为空');
    }

    if (!config.textColor) {
      errors.push('文字颜色不能为空');
    }

    if (!config.fontFamily) {
      errors.push('字体不能为空');
    }

    if (!config.fontSize || config.fontSize < 8 || config.fontSize > 72) {
      errors.push('字体大小必须在8-72之间');
    }

    if (!config.labelCount || config.labelCount < 1 || config.labelCount > 100) {
      errors.push('标签数量必须在1-100之间');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 预设模板数据
   */
  static getPresetTemplateData() {
    return [
      {
        name: '18k纸黄金',
        config: {
          labelText: '18k纸黄金',
          textColor: '#FFD700',
          fontFamily: '宋体',
          fontSize: 32,
          fontWeight: 'bold'
        }
      },
      {
        name: '黄金双冠',
        config: {
          labelText: '黄金双冠',
          textColor: '#FFD700',
          fontFamily: '宋体',
          fontSize: 32,
          fontWeight: 'bold'
        }
      },
      {
        name: '金砖九冠',
        config: {
          labelText: '金砖九冠',
          textColor: '#FFD700',
          fontFamily: '宋体',
          fontSize: 32,
          fontWeight: 'bold'
        }
      },
      {
        name: '霸王花',
        config: {
          labelText: '霸王花',
          textColor: '#FF0000',
          fontFamily: '楷体',
          fontSize: 28,
          fontWeight: 'bold'
        }
      },
      {
        name: '绿砖',
        config: {
          labelText: '绿钻',
          textColor: '#00FF00',
          fontFamily: '宋体',
          fontSize: 32,
          fontWeight: 'bold'
        }
      },
      {
        name: '桃园三结义',
        config: {
          labelText: '桃园三结义',
          textColor: '#00B83F',
          fontFamily: '华文行楷',
          fontSize: 24,
          fontWeight: 'normal'
        }
      },
      {
        name: '金观音',
        config: {
          labelText: '金观音',
          textColor: '#aa8c30',
          fontFamily: '华文行楷',
          fontSize: 28,
          fontWeight: 'normal'
        }
      },
      {
        name: '红钻之光',
        config: {
          labelText: '红钻之光',
          textColor: '#f16d7e',
          fontFamily: '宋体',
          fontSize: 26,
          fontWeight: 'normal',
          enableShadow: true,
          shadowConfig: {
            offsetX: 0,
            offsetY: 0,
            blurRadius: 1,
            shadowColor: '#ff0000'
          }
        }
      }
    ];
  }
}

export default colorLabelApi;
