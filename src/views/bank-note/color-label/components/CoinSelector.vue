<template>
  <div class="coin-selector">
    <el-card header="选择钱币">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索钱币（序列号、年份、面额等）"
          @input="handleSearch"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="handleBatchSelect">
          <el-icon><Plus /></el-icon>
          批量选择
        </el-button>
      </div>

      <!-- 筛选器 -->
      <div class="filter-bar">
        <el-row :gutter="10">
          <el-col :span="6">
            <el-select v-model="filters.year" placeholder="年份" clearable @change="handleFilter">
              <el-option
                v-for="year in yearOptions"
                :key="year"
                :label="year"
                :value="year"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filters.denomination" placeholder="面额" clearable @change="handleFilter">
              <el-option
                v-for="denom in denominationOptions"
                :key="denom"
                :label="denom"
                :value="denom"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filters.score" placeholder="评分" clearable @change="handleFilter">
              <el-option
                v-for="score in scoreOptions"
                :key="score"
                :label="score"
                :value="score"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filters.gradeType" placeholder="评级类型" clearable @change="handleFilter">
              <el-option label="EPQ" value="EPQ" />
              <el-option label="PMG" value="PMG" />
              <el-option label="PCGS" value="PCGS" />
            </el-select>
          </el-col>
        </el-row>
      </div>

      <!-- 已选择的钱币 -->
      <div v-if="selectedCoins.length > 0" class="selected-coins">
        <div class="selected-header">
          <span>已选择 {{ selectedCoins.length }} 个钱币</span>
          <el-button size="small" text type="danger" @click="clearSelection">
            <el-icon><Delete /></el-icon>
            清空选择
          </el-button>
        </div>
        <div class="selected-list">
          <el-tag
            v-for="coin in selectedCoins"
            :key="coin.id"
            closable
            @close="removeCoin(coin.id)"
            class="selected-coin-tag"
          >
            {{ coin.serialNumber }} - {{ coin.year }} {{ coin.denomination }}
          </el-tag>
        </div>
      </div>

      <!-- 钱币列表 -->
      <div class="coin-list">
        <el-table
          :data="filteredCoins"
          @selection-change="handleSelectionChange"
          v-loading="loading"
          height="400"
          size="small"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="serialNumber" label="序列号" width="120" />
          <el-table-column prop="year" label="年份" width="80" />
          <el-table-column prop="denomination" label="面额" width="80" />
          <el-table-column prop="description" label="描述" min-width="150" />
          <el-table-column prop="score" label="评分" width="60" />
          <el-table-column prop="gradeType" label="评级" width="60" />
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button
                size="small"
                text
                type="primary"
                @click="previewCoin(row)"
              >
                预览
              </el-button>
              <el-button
                size="small"
                text
                :type="isSelected(row.id) ? 'danger' : 'success'"
                @click="toggleCoin(row)"
              >
                {{ isSelected(row.id) ? '移除' : '选择' }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 批量选择对话框 -->
    <el-dialog
      v-model="showBatchDialog"
      title="批量选择钱币"
      width="600px"
    >
      <el-form>
        <el-form-item label="选择方式">
          <el-radio-group v-model="batchSelectType">
            <el-radio label="range">序列号范围</el-radio>
            <el-radio label="list">序列号列表</el-radio>
            <el-radio label="condition">条件筛选</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="batchSelectType === 'range'" label="序列号范围">
          <el-row :gutter="10">
            <el-col :span="11">
              <el-input v-model="batchConfig.startSerial" placeholder="起始序列号" />
            </el-col>
            <el-col :span="2" class="text-center">至</el-col>
            <el-col :span="11">
              <el-input v-model="batchConfig.endSerial" placeholder="结束序列号" />
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item v-if="batchSelectType === 'list'" label="序列号列表">
          <el-input
            v-model="batchConfig.serialList"
            type="textarea"
            :rows="4"
            placeholder="请输入序列号，每行一个或用逗号分隔"
          />
        </el-form-item>

        <el-form-item v-if="batchSelectType === 'condition'" label="筛选条件">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-select v-model="batchConfig.conditionYear" placeholder="年份">
                <el-option
                  v-for="year in yearOptions"
                  :key="year"
                  :label="year"
                  :value="year"
                />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-select v-model="batchConfig.conditionDenom" placeholder="面额">
                <el-option
                  v-for="denom in denominationOptions"
                  :key="denom"
                  :label="denom"
                  :value="denom"
                />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-select v-model="batchConfig.conditionScore" placeholder="评分">
                <el-option
                  v-for="score in scoreOptions"
                  :key="score"
                  :label="score"
                  :value="score"
                />
              </el-select>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showBatchDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmBatchSelect">确认选择</el-button>
      </template>
    </el-dialog>

    <!-- 钱币预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      title="钱币预览"
      width="400px"
    >
      <div v-if="previewCoinData" class="coin-preview">
        <div class="preview-item">
          <span class="label">序列号：</span>
          <span class="value">{{ previewCoinData.serialNumber }}</span>
        </div>
        <div class="preview-item">
          <span class="label">年份面额：</span>
          <span class="value">{{ previewCoinData.year }} {{ previewCoinData.denomination }}</span>
        </div>
        <div class="preview-item">
          <span class="label">描述：</span>
          <span class="value">{{ previewCoinData.description }}</span>
        </div>
        <div class="preview-item">
          <span class="label">评分：</span>
          <span class="value">{{ previewCoinData.score }} {{ previewCoinData.gradeType }}</span>
        </div>
        <div class="preview-item">
          <span class="label">评级描述：</span>
          <span class="value">{{ previewCoinData.gradeDescription }}</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { Search, Plus, Delete } from '@element-plus/icons-vue';
import { EleMessage } from 'ele-admin-plus/es';

const emit = defineEmits(['selection-change', 'coin-preview']);

// 响应式数据
const searchKeyword = ref('');
const loading = ref(false);
const showBatchDialog = ref(false);
const showPreviewDialog = ref(false);
const batchSelectType = ref('range');
const previewCoinData = ref(null);

const filters = reactive({
  year: '',
  denomination: '',
  score: '',
  gradeType: ''
});

const batchConfig = reactive({
  startSerial: '',
  endSerial: '',
  serialList: '',
  conditionYear: '',
  conditionDenom: '',
  conditionScore: ''
});

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
});

const selectedCoins = ref([]);
const coinList = ref([]);

// 模拟数据
const mockCoins = ref([
  {
    id: '1',
    serialNumber: 'FZ-********',
    year: '1980年',
    denomination: '贰角',
    description: '民族人物头像',
    score: '68',
    gradeType: 'EPQ',
    gradeDescription: 'Superb Gem Unc',
    bankName: '中国人民银行',
    qrCode: 'ZK25080001'
  },
  {
    id: '2',
    serialNumber: 'FZ-********',
    year: '1980年',
    denomination: '贰角',
    description: '民族人物头像',
    score: '68',
    gradeType: 'EPQ',
    gradeDescription: 'Superb Gem Unc',
    bankName: '中国人民银行',
    qrCode: 'ZK25080002'
  },
  // 更多模拟数据...
]);

// 计算属性
const filteredCoins = computed(() => {
  let result = coinList.value;

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(coin =>
      coin.serialNumber.toLowerCase().includes(keyword) ||
      coin.year.toLowerCase().includes(keyword) ||
      coin.denomination.toLowerCase().includes(keyword) ||
      coin.description.toLowerCase().includes(keyword)
    );
  }

  // 条件过滤
  if (filters.year) {
    result = result.filter(coin => coin.year === filters.year);
  }
  if (filters.denomination) {
    result = result.filter(coin => coin.denomination === filters.denomination);
  }
  if (filters.score) {
    result = result.filter(coin => coin.score === filters.score);
  }
  if (filters.gradeType) {
    result = result.filter(coin => coin.gradeType === filters.gradeType);
  }

  return result;
});

const yearOptions = computed(() => {
  return [...new Set(coinList.value.map(coin => coin.year))];
});

const denominationOptions = computed(() => {
  return [...new Set(coinList.value.map(coin => coin.denomination))];
});

const scoreOptions = computed(() => {
  return [...new Set(coinList.value.map(coin => coin.score))];
});

// 生命周期
onMounted(() => {
  loadCoins();
});

// 方法
const loadCoins = async () => {
  loading.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    coinList.value = mockCoins.value;
    pagination.total = coinList.value.length;
  } catch (error) {
    EleMessage.error('加载钱币数据失败：' + error.message);
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  pagination.currentPage = 1;
};

const handleFilter = () => {
  pagination.currentPage = 1;
};

const handleSelectionChange = (selection) => {
  selectedCoins.value = selection;
  emit('selection-change', selection);
};

const isSelected = (coinId) => {
  return selectedCoins.value.some(coin => coin.id === coinId);
};

const toggleCoin = (coin) => {
  const index = selectedCoins.value.findIndex(c => c.id === coin.id);
  if (index > -1) {
    selectedCoins.value.splice(index, 1);
  } else {
    selectedCoins.value.push(coin);
  }
  emit('selection-change', selectedCoins.value);
};

const removeCoin = (coinId) => {
  const index = selectedCoins.value.findIndex(c => c.id === coinId);
  if (index > -1) {
    selectedCoins.value.splice(index, 1);
    emit('selection-change', selectedCoins.value);
  }
};

const clearSelection = () => {
  selectedCoins.value = [];
  emit('selection-change', []);
};

const previewCoin = (coin) => {
  previewCoinData.value = coin;
  showPreviewDialog.value = true;
  emit('coin-preview', coin);
};

const handleBatchSelect = () => {
  showBatchDialog.value = true;
};

const confirmBatchSelect = () => {
  // 实现批量选择逻辑
  let coinsToAdd = [];

  switch (batchSelectType.value) {
    case 'range':
      // 序列号范围选择
      coinsToAdd = coinList.value.filter(coin => {
        const serial = coin.serialNumber;
        return serial >= batchConfig.startSerial && serial <= batchConfig.endSerial;
      });
      break;
    case 'list':
      // 序列号列表选择
      const serialNumbers = batchConfig.serialList
        .split(/[,\n]/)
        .map(s => s.trim())
        .filter(s => s);
      coinsToAdd = coinList.value.filter(coin =>
        serialNumbers.includes(coin.serialNumber)
      );
      break;
    case 'condition':
      // 条件筛选选择
      coinsToAdd = coinList.value.filter(coin => {
        return (!batchConfig.conditionYear || coin.year === batchConfig.conditionYear) &&
               (!batchConfig.conditionDenom || coin.denomination === batchConfig.conditionDenom) &&
               (!batchConfig.conditionScore || coin.score === batchConfig.conditionScore);
      });
      break;
  }

  // 添加到选择列表（去重）
  coinsToAdd.forEach(coin => {
    if (!isSelected(coin.id)) {
      selectedCoins.value.push(coin);
    }
  });

  emit('selection-change', selectedCoins.value);
  showBatchDialog.value = false;
  EleMessage.success(`已添加 ${coinsToAdd.length} 个钱币到选择列表`);
};

const handleSizeChange = (size) => {
  pagination.pageSize = size;
  pagination.currentPage = 1;
};

const handleCurrentChange = (page) => {
  pagination.currentPage = page;
};

// 暴露方法给父组件
defineExpose({
  getSelectedCoins: () => selectedCoins.value,
  clearSelection,
  loadCoins
});
</script>

<style scoped>
.coin-selector {
  height: 100%;
}

.search-bar {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
}

.search-bar .el-input {
  flex: 1;
}

.filter-bar {
  margin-bottom: 16px;
}

.selected-coins {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
}

.selected-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
}

.selected-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-coin-tag {
  margin: 0;
}

.coin-list {
  margin-bottom: 16px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.coin-preview {
  padding: 16px;
}

.preview-item {
  display: flex;
  margin-bottom: 12px;
  align-items: center;
}

.preview-item .label {
  width: 80px;
  font-weight: 500;
  color: #606266;
}

.preview-item .value {
  flex: 1;
  color: #303133;
}

.text-center {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .search-bar {
    flex-direction: column;
  }
  
  .filter-bar .el-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .selected-list {
    flex-direction: column;
  }
}
</style>
