<template>
  <div class="color-label-designer">
    <!-- 文本输入 -->
    <div class="form-group">
      <label class="form-label">标签文本</label>
      <el-input
        v-model="config.labelText"
        placeholder="请输入标签文本"
        @input="handleTextChange"
        maxlength="50"
        show-word-limit
        clearable
      />
    </div>

    <!-- 颜色选择器 -->
    <div class="form-group">
      <label class="form-label">文字颜色</label>
      <div class="color-input-group">
        <el-color-picker
          v-model="config.textColor"
          @change="handleColorChange"
          show-alpha
          size="default"
        />
        <el-input
          v-model="config.textColor"
          placeholder="#0000FF"
          class="color-input"
          @change="handleColorChange"
        />
      </div>
    </div>

    <!-- 字体设置 -->
    <div class="form-group">
      <label class="form-label">字体设置</label>
      <el-row :gutter="10">
        <el-col :span="14">
          <el-select
            v-model="config.fontFamily"
            placeholder="选择字体"
            @change="handleFontChange"
            filterable
          >
            <el-option
              v-for="font in fontList"
              :key="font.value"
              :label="font.name"
              :value="font.value"
            />
          </el-select>
        </el-col>
        <el-col :span="10">
          <el-input-number
            v-model="config.fontSize"
            :min="8"
            :max="72"
            @change="handleFontSizeChange"
            controls-position="right"
            size="default"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 字体控制按钮 -->
    <div class="form-group">
      <label class="form-label">字体控制</label>
      <div class="font-controls">
        <el-button-group>
          <el-button @click="decreaseFontSize" :disabled="config.fontSize <= 8">
            <el-icon><Minus /></el-icon>
            小
          </el-button>
          <el-button 
            @click="toggleFontWeight" 
            :type="config.fontWeight === 'bold' ? 'primary' : 'default'"
          >
            <strong>B</strong>
          </el-button>
          <el-button @click="increaseFontSize" :disabled="config.fontSize >= 72">
            <el-icon><Plus /></el-icon>
            大
          </el-button>
          <el-button @click="autoAdjustFont" type="info">
            <el-icon><MagicStick /></el-icon>
            智能
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 标签数量 -->
    <div class="form-group">
      <label class="form-label">标签数量</label>
      <el-input-number
        v-model="config.labelCount"
        :min="1"
        :max="100"
        @change="handleCountChange"
        controls-position="right"
        size="default"
        style="width: 100%"
      />
    </div>

    <!-- 高级设置 -->
    <div class="form-group">
      <label class="form-label">高级设置</label>
      <el-collapse v-model="activeAdvanced">
        <el-collapse-item title="阴影效果" name="shadow">
          <div class="advanced-setting">
            <el-switch
              v-model="config.enableShadow"
              @change="handleShadowToggle"
            />
            <div v-if="config.enableShadow" class="shadow-settings">
              <el-row :gutter="10">
                <el-col :span="12">
                  <el-input-number
                    v-model="shadowConfig.offsetX"
                    placeholder="水平偏移"
                    size="small"
                    @change="updateShadow"
                  />
                </el-col>
                <el-col :span="12">
                  <el-input-number
                    v-model="shadowConfig.offsetY"
                    placeholder="垂直偏移"
                    size="small"
                    @change="updateShadow"
                  />
                </el-col>
              </el-row>
              <el-row :gutter="10" style="margin-top: 10px">
                <el-col :span="12">
                  <el-input-number
                    v-model="shadowConfig.blurRadius"
                    placeholder="模糊半径"
                    size="small"
                    @change="updateShadow"
                  />
                </el-col>
                <el-col :span="12">
                  <el-color-picker
                    v-model="shadowConfig.shadowColor"
                    @change="updateShadow"
                    size="small"
                  />
                </el-col>
              </el-row>
            </div>
          </div>
        </el-collapse-item>

        <el-collapse-item title="边框效果" name="border">
          <div class="advanced-setting">
            <el-switch
              v-model="config.enableBorder"
              @change="handleBorderToggle"
            />
            <div v-if="config.enableBorder" class="border-settings">
              <el-row :gutter="10">
                <el-col :span="8">
                  <el-input-number
                    v-model="borderConfig.borderWidth"
                    placeholder="宽度"
                    size="small"
                    @change="updateBorder"
                  />
                </el-col>
                <el-col :span="8">
                  <el-select
                    v-model="borderConfig.borderStyle"
                    placeholder="样式"
                    size="small"
                    @change="updateBorder"
                  >
                    <el-option label="实线" value="solid" />
                    <el-option label="虚线" value="dashed" />
                    <el-option label="点线" value="dotted" />
                  </el-select>
                </el-col>
                <el-col :span="8">
                  <el-color-picker
                    v-model="borderConfig.borderColor"
                    @change="updateBorder"
                    size="small"
                  />
                </el-col>
              </el-row>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <!-- 操作按钮 -->
    <div class="form-group">
      <div class="action-buttons">
        <el-button type="primary" @click="handlePreview" :loading="previewLoading">
          <el-icon><View /></el-icon>
          预览
        </el-button>
        <el-button type="success" @click="handlePrint" :loading="printLoading">
          <el-icon><Printer /></el-icon>
          打印
        </el-button>
        <el-button type="info" @click="handleSaveTemplate">
          <el-icon><DocumentAdd /></el-icon>
          保存模板
        </el-button>
        <el-button @click="handleReset">
          <el-icon><RefreshLeft /></el-icon>
          重置
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { 
  Plus, 
  Minus, 
  MagicStick, 
  View, 
  Printer, 
  DocumentAdd, 
  RefreshLeft 
} from '@element-plus/icons-vue';
import { EleMessage } from 'ele-admin-plus/es';
import { colorLabelApi, ColorLabelUtils } from '../api';

const emit = defineEmits(['preview', 'print', 'save-template']);

// 响应式数据
const config = reactive({
  labelText: '',
  textColor: '#0000FF',
  backgroundColor: '',
  fontFamily: '青鸟华光简行楷',
  fontSize: 40,
  fontWeight: 'normal',
  fontStyle: 'normal',
  textAlign: 'center',
  lineHeight: 1.2,
  letterSpacing: 0,
  labelCount: 10,
  enableShadow: false,
  shadowConfig: null,
  enableBorder: false,
  borderConfig: null
});

const shadowConfig = reactive({
  offsetX: 0,
  offsetY: 0,
  blurRadius: 1,
  shadowColor: '#000000'
});

const borderConfig = reactive({
  borderWidth: 1,
  borderStyle: 'solid',
  borderColor: '#000000',
  borderRadius: 0
});

const fontList = ref([]);
const activeAdvanced = ref([]);
const previewLoading = ref(false);
const printLoading = ref(false);

// 生命周期
onMounted(async () => {
  await loadFontList();
});

// 监听配置变化
watch(() => config.labelText, (newText) => {
  if (newText) {
    autoAdjustFont();
  }
}, { immediate: false });

// 方法
const loadFontList = async () => {
  try {
    const fonts = await colorLabelApi.getFontList();
    fontList.value = fonts;
  } catch (error) {
    console.error('加载字体列表失败:', error);
    // 使用默认字体列表
    fontList.value = [
      { name: '青鸟华光简行楷', value: '青鸟华光简行楷' },
      { name: '宋体', value: 'SimSun' },
      { name: '黑体', value: 'SimHei' },
      { name: '微软雅黑', value: 'Microsoft Yahei' },
      { name: '楷体', value: 'KaiTi' },
      { name: '华文行楷', value: 'STXingkai' }
    ];
  }
};

const handleTextChange = () => {
  // 文本变化时的处理
  emitChange();
};

const handleColorChange = () => {
  emitChange();
};

const handleFontChange = () => {
  emitChange();
};

const handleFontSizeChange = () => {
  emitChange();
};

const handleCountChange = () => {
  emitChange();
};

const decreaseFontSize = () => {
  if (config.fontSize > 8) {
    config.fontSize--;
    emitChange();
  }
};

const increaseFontSize = () => {
  if (config.fontSize < 72) {
    config.fontSize++;
    emitChange();
  }
};

const toggleFontWeight = () => {
  config.fontWeight = config.fontWeight === 'bold' ? 'normal' : 'bold';
  emitChange();
};

const autoAdjustFont = () => {
  if (config.labelText) {
    config.fontSize = ColorLabelUtils.getRecommendedFontSize(config.labelText);
    emitChange();
  }
};

const handleShadowToggle = () => {
  if (config.enableShadow) {
    config.shadowConfig = { ...shadowConfig };
  } else {
    config.shadowConfig = null;
  }
  emitChange();
};

const updateShadow = () => {
  if (config.enableShadow) {
    config.shadowConfig = { ...shadowConfig };
    emitChange();
  }
};

const handleBorderToggle = () => {
  if (config.enableBorder) {
    config.borderConfig = { ...borderConfig };
  } else {
    config.borderConfig = null;
  }
  emitChange();
};

const updateBorder = () => {
  if (config.enableBorder) {
    config.borderConfig = { ...borderConfig };
    emitChange();
  }
};

const handlePreview = async () => {
  const validation = ColorLabelUtils.validateConfig(config);
  if (!validation.isValid) {
    EleMessage.error(validation.errors[0]);
    return;
  }

  previewLoading.value = true;
  try {
    emit('preview', { ...config });
  } finally {
    previewLoading.value = false;
  }
};

const handlePrint = async () => {
  const validation = ColorLabelUtils.validateConfig(config);
  if (!validation.isValid) {
    EleMessage.error(validation.errors[0]);
    return;
  }

  printLoading.value = true;
  try {
    emit('print', { ...config });
  } finally {
    printLoading.value = false;
  }
};

const handleSaveTemplate = () => {
  const validation = ColorLabelUtils.validateConfig(config);
  if (!validation.isValid) {
    EleMessage.error(validation.errors[0]);
    return;
  }

  emit('save-template', { ...config });
};

const handleReset = () => {
  Object.assign(config, {
    labelText: '',
    textColor: '#0000FF',
    backgroundColor: '',
    fontFamily: '青鸟华光简行楷',
    fontSize: 40,
    fontWeight: 'normal',
    fontStyle: 'normal',
    textAlign: 'center',
    lineHeight: 1.2,
    letterSpacing: 0,
    labelCount: 10,
    enableShadow: false,
    shadowConfig: null,
    enableBorder: false,
    borderConfig: null
  });
  
  activeAdvanced.value = [];
  emitChange();
};

const emitChange = () => {
  // 实时更新预览
  if (config.labelText.trim()) {
    setTimeout(() => {
      emit('preview', { ...config });
    }, 100); // 防抖处理
  }
};

// 暴露方法给父组件
const updateColor = (color) => {
  config.textColor = color;
  emitChange();
};

const applyTemplate = (template) => {
  Object.assign(config, template.config || template);
  emitChange();
};

defineExpose({
  updateColor,
  applyTemplate,
  getConfig: () => ({ ...config })
});
</script>

<style scoped>
.color-label-designer {
  padding: 0;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.color-input-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

.color-input {
  flex: 1;
}

.font-controls {
  width: 100%;
}

.font-controls .el-button-group {
  width: 100%;
}

.font-controls .el-button {
  flex: 1;
}

.advanced-setting {
  padding: 10px 0;
}

.shadow-settings,
.border-settings {
  margin-top: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.action-buttons .el-button {
  margin: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .action-buttons {
    grid-template-columns: 1fr;
  }
}
</style>
