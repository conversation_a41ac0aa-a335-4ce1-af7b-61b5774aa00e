<template>
  <div class="color-presets">
    <div v-if="!loading && colorPresets.length > 0" class="presets-container">
      <div
        v-for="color in colorPresets"
        :key="color?.value || Math.random()"
        class="color-preset-item"
        @click="selectColor(color?.value)"
        :title="`${color?.name || '未知颜色'}: ${color?.value || '#000000'}`"
      >
        <div
          class="color-preview"
          :style="{ backgroundColor: color?.value || '#000000' }"
        ></div>
        <div class="color-info">
          <div class="color-name">{{ color?.name || '未知颜色' }}</div>
          <div class="color-value">{{ color?.value || '#000000' }}</div>
          <div v-if="color?.description" class="color-description">
            {{ color.description }}
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && colorPresets.length === 0" class="empty-container">
      <el-empty description="暂无颜色预设" :image-size="60" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import { colorLabelApi } from '../api';

const emit = defineEmits(['color-select']);

// 响应式数据
const colorPresets = ref([]);
const loading = ref(true);

// 生命周期
onMounted(async () => {
  await loadColorPresets();
});

// 方法
const loadColorPresets = async () => {
  try {
    loading.value = true;
    const colors = await colorLabelApi.getColorPresets();
    colorPresets.value = colors;
  } catch (error) {
    console.error('加载颜色预设失败:', error);
    // 使用默认颜色预设
    colorPresets.value = getDefaultColorPresets();
  } finally {
    loading.value = false;
  }
};

const selectColor = (color) => {
  if (!color) return;
  emit('color-select', color);
  EleMessage.success(`已选择颜色：${color}`);
};

const getDefaultColorPresets = () => {
  return [
    {
      name: '大红色',
      value: '#FF0000',
      description: '纵二横三：补号，幼线体，数字冠，大王冠，首发冠'
    },
    {
      name: '深红色',
      value: '#980000',
      description: '纵二横二：浴火凤凰'
    },
    {
      name: '黑色',
      value: '#000000',
      description: '纵一：满堂彩，炭黑，深版'
    },
    {
      name: '绿色',
      value: '#00FF00',
      description: '纵三：绿幽灵，绿钻'
    },
    {
      name: '背绿',
      value: '#95db95',
      description: ''
    },
    {
      name: '深青色',
      value: '#B9D7A8',
      description: '纵七：青绿美翠，五彩苍松，苍松翠鹤'
    },
    {
      name: '浅蓝色',
      value: '#0096db',
      description: '玉钩国'
    },
    {
      name: '天蓝色',
      value: '#00FFFF',
      description: '纵三横二：天蓝色，蓝凤朝阳'
    },
    {
      name: '蓝色',
      value: '#4A86E8',
      description: '纵二横一：蓝天白云，蓝色妖姬'
    },
    {
      name: '深蓝色',
      value: '#0000FF',
      description: '纵一横一：深蓝'
    },
    {
      name: '紫色',
      value: '#9900FF',
      description: '纵一横二：紫气东来'
    },
    {
      name: '粉色',
      value: '#FF69B4',
      description: '纵二横五：粉色佳人'
    },
    {
      name: '桃红色',
      value: '#FF1493',
      description: '金杯桃花红'
    },
    {
      name: '橙色',
      value: '#FF9900',
      description: '纵二横四：金牡丹，红金龙，金龙王，金光蓝鹤'
    },
    {
      name: '金星绿波',
      value: '#e68e09',
      description: ''
    }
  ];
};
</script>

<style scoped>
.color-presets {
  height: 100%;
}

.presets-container {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 5px;
}

.color-preset-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  margin-bottom: 8px;
}

.color-preset-item:hover {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.color-preset-item:active {
  transform: translateY(0);
}

.color-preview {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 2px solid #fff;
  box-shadow: 0 0 0 1px #dcdfe6;
  margin-right: 12px;
  flex-shrink: 0;
}

.color-info {
  flex: 1;
  min-width: 0;
}

.color-name {
  font-weight: 600;
  font-size: 14px;
  color: #303133;
  margin-bottom: 2px;
}

.color-value {
  font-size: 12px;
  color: #909399;
  font-family: 'Courier New', monospace;
  margin-bottom: 4px;
}

.color-description {
  font-size: 11px;
  color: #606266;
  line-height: 1.4;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.loading-container {
  padding: 20px;
}

.empty-container {
  padding: 40px 20px;
  text-align: center;
}

/* 滚动条样式 */
.presets-container::-webkit-scrollbar {
  width: 6px;
}

.presets-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.presets-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.presets-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .color-preset-item {
    padding: 10px;
  }

  .color-preview {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }

  .color-name {
    font-size: 13px;
  }

  .color-value {
    font-size: 11px;
  }

  .color-description {
    font-size: 10px;
  }
}
</style>
