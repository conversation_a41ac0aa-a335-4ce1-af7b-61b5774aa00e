<template>
  <el-dialog
    v-model="visible"
    title="打印预览"
    width="90%"
    :before-close="handleClose"
    class="print-preview-dialog"
  >
    <!-- 工具栏 -->
    <div class="preview-toolbar">
      <div class="toolbar-left">
        <el-tag type="info">{{ printData?.labels?.length || 0 }}个标签</el-tag>
        <el-tag type="success">A4纸张</el-tag>
      </div>
      <div class="toolbar-right">
        <el-button-group size="small">
          <el-button @click="zoomOut" :disabled="zoomLevel <= 0.5">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
          <el-button @click="resetZoom">
            {{ Math.round(zoomLevel * 100) }}%
          </el-button>
          <el-button @click="zoomIn" :disabled="zoomLevel >= 1.5">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 预览内容 -->
    <div class="preview-container">
      <div 
        class="preview-content" 
        :style="{ transform: `scale(${zoomLevel})` }"
        v-html="printData?.html"
      ></div>
    </div>

    <!-- 打印设置 -->
    <div class="print-settings">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="setting-item">
            <label>纸张方向:</label>
            <el-radio-group v-model="printOptions.orientation" size="small">
              <el-radio label="portrait">纵向</el-radio>
              <el-radio label="landscape">横向</el-radio>
            </el-radio-group>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="setting-item">
            <label>页边距:</label>
            <el-select v-model="printOptions.margin" size="small">
              <el-option label="默认 (10mm)" value="10mm" />
              <el-option label="窄 (5mm)" value="5mm" />
              <el-option label="宽 (15mm)" value="15mm" />
            </el-select>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="setting-item">
            <label>打印质量:</label>
            <el-select v-model="printOptions.quality" size="small">
              <el-option label="标准" value="normal" />
              <el-option label="高质量" value="high" />
              <el-option label="草稿" value="draft" />
            </el-select>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 对话框底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="info" @click="handleExportPdf" :loading="exporting">
          <el-icon><Download /></el-icon>
          导出PDF
        </el-button>
        <el-button type="primary" @click="handlePrint" :loading="printing">
          <el-icon><Printer /></el-icon>
          确认打印
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { 
  ZoomIn, 
  ZoomOut, 
  Printer, 
  Download 
} from '@element-plus/icons-vue';
import { EleMessage } from 'ele-admin-plus/es';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  printData: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['update:modelValue', 'confirm-print', 'export-pdf']);

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const zoomLevel = ref(0.8);
const printing = ref(false);
const exporting = ref(false);

const printOptions = reactive({
  orientation: 'portrait',
  margin: '10mm',
  quality: 'normal'
});

// 监听打印数据变化
watch(() => props.printData, (newData) => {
  if (newData) {
    // 重置缩放级别
    zoomLevel.value = 0.8;
  }
});

// 方法
const zoomIn = () => {
  if (zoomLevel.value < 1.5) {
    zoomLevel.value = Math.min(zoomLevel.value + 0.1, 1.5);
  }
};

const zoomOut = () => {
  if (zoomLevel.value > 0.5) {
    zoomLevel.value = Math.max(zoomLevel.value - 0.1, 0.5);
  }
};

const resetZoom = () => {
  zoomLevel.value = 0.8;
};

const handleClose = () => {
  visible.value = false;
};

const handlePrint = async () => {
  if (!props.printData) {
    EleMessage.error('没有可打印的数据');
    return;
  }

  printing.value = true;
  try {
    emit('confirm-print', printOptions);
  } finally {
    printing.value = false;
  }
};

const handleExportPdf = async () => {
  if (!props.printData) {
    EleMessage.error('没有可导出的数据');
    return;
  }

  exporting.value = true;
  try {
    // 创建一个新窗口用于PDF导出
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      throw new Error('无法打开新窗口，请检查浏览器弹窗设置');
    }

    // 写入打印内容
    printWindow.document.write(props.printData.html);
    printWindow.document.close();

    // 等待内容加载完成后触发打印对话框
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print();
      }, 500);
    };

    emit('export-pdf', printOptions);
    EleMessage.success('PDF导出窗口已打开');
  } catch (error) {
    EleMessage.error('导出失败：' + error.message);
  } finally {
    exporting.value = false;
  }
};
</script>

<style scoped>
.print-preview-dialog {
  --el-dialog-padding-primary: 0;
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafbfc;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-container {
  height: 60vh;
  overflow: auto;
  padding: 20px;
  background: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.preview-content {
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  transform-origin: top center;
  transition: transform 0.2s ease;
  min-width: 210mm;
  min-height: 297mm;
  padding: 10mm;
}

.print-settings {
  padding: 20px;
  border-top: 1px solid #e4e7ed;
  background: #fafbfc;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-item label {
  font-size: 14px;
  color: #606266;
  min-width: 70px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #e4e7ed;
  background: #fafbfc;
}

/* 滚动条样式 */
.preview-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.preview-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.preview-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.preview-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 打印样式 */
:deep(.print-container) {
  display: flex;
  flex-wrap: wrap;
  gap: 5mm;
}

:deep(.print-label) {
  padding: 3mm;
  border: 1px solid #ccc;
  width: 40mm;
  height: 20mm;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  page-break-inside: avoid;
  background: white;
  border-radius: 2px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .preview-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
  
  .preview-container {
    height: 50vh;
    padding: 10px;
  }
  
  .preview-content {
    min-width: 180mm;
    min-height: 250mm;
    padding: 5mm;
  }
  
  .print-settings .el-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .setting-item label {
    min-width: auto;
  }
}
</style>
