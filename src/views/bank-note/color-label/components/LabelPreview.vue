<template>
  <div class="label-preview">
    <!-- 预览工具栏 -->
    <div class="preview-toolbar">
      <div class="toolbar-left">
        <span class="preview-title">实时预览</span>
        <el-tag v-if="config.labelCount" size="small" type="info">
          {{ config.labelCount }}个标签
        </el-tag>
      </div>
      <div class="toolbar-right">
        <el-button-group size="small">
          <el-button @click="zoomOut" :disabled="zoomLevel <= 0.5">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
          <el-button @click="resetZoom">
            {{ Math.round(zoomLevel * 100) }}%
          </el-button>
          <el-button @click="zoomIn" :disabled="zoomLevel >= 2">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
        </el-button-group>
        <el-button size="small" @click="refreshPreview" :loading="refreshing">
          <el-icon><Refresh /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 预览内容区域 -->
    <div class="preview-content" :style="{ transform: `scale(${zoomLevel})` }">
      <!-- 有内容时显示预览 -->
      <div v-if="hasContent" class="labels-container">
        <div
          v-for="(item, index) in previewLabels"
          :key="index"
          class="label-item"
          :style="getLabelStyle()"
        >
          {{ config.labelText }}
        </div>
      </div>

      <!-- 无内容时显示提示 -->
      <div v-else class="empty-preview">
        <el-empty description="请输入标签文本开始设计">
          <template #image>
            <el-icon size="64" color="#dcdfe6">
              <EditPen />
            </el-icon>
          </template>
        </el-empty>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-overlay">
        <el-loading-spinner />
        <span>生成预览中...</span>
      </div>
    </div>

    <!-- 预览信息面板 -->
    <div v-if="hasContent" class="preview-info">
      <div class="info-item">
        <span class="info-label">文字颜色:</span>
        <span class="info-value" :style="{ color: config.textColor }">
          {{ config.textColor }}
        </span>
      </div>
      <div class="info-item">
        <span class="info-label">字体:</span>
        <span class="info-value">{{ config.fontFamily }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">字号:</span>
        <span class="info-value">{{ config.fontSize }}px</span>
      </div>
      <div class="info-item">
        <span class="info-label">数量:</span>
        <span class="info-value">{{ config.labelCount }}个</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue';
import { ZoomIn, ZoomOut, Refresh, EditPen } from '@element-plus/icons-vue';
import { ColorLabelUtils } from '../api';

const props = defineProps({
  config: {
    type: Object,
    default: () => ({})
  },
  previewData: {
    type: Object,
    default: null
  }
});

// 响应式数据
const zoomLevel = ref(1);
const loading = ref(false);
const refreshing = ref(false);

// 计算属性
const hasContent = computed(() => {
  return props.config.labelText && props.config.labelText.trim() !== '';
});

const previewLabels = computed(() => {
  if (!hasContent.value) return [];
  const count = Math.min(props.config.labelCount || 10, 20); // 预览最多显示20个
  return Array.from({ length: count }, (_, i) => i);
});

// 监听配置变化
watch(() => props.config, () => {
  if (hasContent.value) {
    generatePreview();
  }
}, { deep: true });

// 方法
const getLabelStyle = () => {
  if (!props.config) return {};
  
  return ColorLabelUtils.generateLabelStyle({
    ...props.config,
    // 预览时使用相对较小的尺寸
    fontSize: Math.max(props.config.fontSize * 0.8, 12)
  });
};

const zoomIn = () => {
  if (zoomLevel.value < 2) {
    zoomLevel.value = Math.min(zoomLevel.value + 0.1, 2);
  }
};

const zoomOut = () => {
  if (zoomLevel.value > 0.5) {
    zoomLevel.value = Math.max(zoomLevel.value - 0.1, 0.5);
  }
};

const resetZoom = () => {
  zoomLevel.value = 1;
};

const refreshPreview = async () => {
  if (!hasContent.value) return;
  
  refreshing.value = true;
  try {
    await generatePreview();
  } finally {
    refreshing.value = false;
  }
};

const generatePreview = async () => {
  loading.value = true;
  try {
    // 模拟生成预览的过程
    await nextTick();
    await new Promise(resolve => setTimeout(resolve, 200));
  } finally {
    loading.value = false;
  }
};

// 暴露方法给父组件
const updatePreview = (previewData) => {
  // 处理预览数据更新
  console.log('更新预览数据:', previewData);
};

defineExpose({
  updatePreview,
  refreshPreview,
  zoomIn,
  zoomOut,
  resetZoom
});
</script>

<style scoped>
.label-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafbfc;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.preview-title {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-content {
  flex: 1;
  padding: 20px;
  overflow: auto;
  position: relative;
  transform-origin: top left;
  transition: transform 0.2s ease;
  min-height: 400px;
}

.labels-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: flex-start;
  align-items: flex-start;
}

.label-item {
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-width: 60px;
  min-height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  word-break: break-all;
  transition: all 0.2s ease;
}

.label-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.empty-preview {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 14px;
  color: #606266;
}

.preview-info {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
  background: #fafbfc;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-label {
  font-size: 12px;
  color: #909399;
  min-width: 60px;
}

.info-value {
  font-size: 12px;
  color: #303133;
  font-weight: 500;
  word-break: break-all;
}

/* 滚动条样式 */
.preview-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.preview-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.preview-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.preview-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .preview-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
  
  .preview-content {
    padding: 15px;
  }
  
  .labels-container {
    gap: 8px;
  }
  
  .label-item {
    padding: 6px 10px;
    min-width: 50px;
    min-height: 25px;
  }
  
  .preview-info {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}
</style>
