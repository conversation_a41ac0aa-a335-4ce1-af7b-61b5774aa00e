<template>
  <el-dialog
    v-model="visible"
    title="保存自定义模板"
    width="500px"
    :before-close="handleClose"
  >
    <!-- 表单内容 -->
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-form-item label="模板名称" prop="templateName">
        <el-input
          v-model="form.templateName"
          placeholder="请输入模板名称"
          maxlength="20"
          show-word-limit
          clearable
        />
      </el-form-item>

      <el-form-item label="模板描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          placeholder="请输入模板描述（可选）"
          :rows="3"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="模板预览">
        <div class="template-preview">
          <div
            class="preview-label"
            :style="getPreviewStyle()"
          >
            {{ config.labelText || '预览文本' }}
          </div>
        </div>
      </el-form-item>

      <el-form-item label="模板配置">
        <div class="config-summary">
          <div class="config-item">
            <span class="config-label">文字颜色:</span>
            <span class="config-value" :style="{ color: config.textColor }">
              {{ config.textColor }}
            </span>
          </div>
          <div class="config-item">
            <span class="config-label">字体:</span>
            <span class="config-value">{{ config.fontFamily }}</span>
          </div>
          <div class="config-item">
            <span class="config-label">字号:</span>
            <span class="config-value">{{ config.fontSize }}px</span>
          </div>
          <div class="config-item">
            <span class="config-label">字重:</span>
            <span class="config-value">{{ config.fontWeight || 'normal' }}</span>
          </div>
          <div v-if="config.enableShadow" class="config-item">
            <span class="config-label">阴影:</span>
            <span class="config-value">已启用</span>
          </div>
          <div v-if="config.enableBorder" class="config-item">
            <span class="config-label">边框:</span>
            <span class="config-value">已启用</span>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="保存选项">
        <el-checkbox v-model="form.setAsDefault">设为默认模板</el-checkbox>
        <el-checkbox v-model="form.shareTemplate">允许其他用户使用</el-checkbox>
      </el-form-item>
    </el-form>

    <!-- 对话框底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          <el-icon><DocumentAdd /></el-icon>
          保存模板
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue';
import { DocumentAdd } from '@element-plus/icons-vue';
import { EleMessage } from 'ele-admin-plus/es';
import { colorLabelApi, ColorLabelUtils } from '../api';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  config: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue', 'save-success']);

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const formRef = ref(null);
const saving = ref(false);

const form = reactive({
  templateName: '',
  description: '',
  setAsDefault: false,
  shareTemplate: false
});

// 表单验证规则
const rules = {
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 20, message: '模板名称长度在 2 到 20 个字符', trigger: 'blur' }
  ]
};

// 监听对话框打开
watch(visible, (newVisible) => {
  if (newVisible) {
    // 重置表单
    resetForm();
    // 自动生成模板名称
    generateTemplateName();
  }
});

// 方法
const resetForm = () => {
  Object.assign(form, {
    templateName: '',
    description: '',
    setAsDefault: false,
    shareTemplate: false
  });
  
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate();
    }
  });
};

const generateTemplateName = () => {
  if (props.config.labelText) {
    // 基于标签文本生成模板名称
    const text = props.config.labelText.substring(0, 10);
    const timestamp = new Date().toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).replace(/[\/\s:]/g, '');
    form.templateName = `${text}_${timestamp}`;
  }
};

const getPreviewStyle = () => {
  return ColorLabelUtils.generateLabelStyle({
    ...props.config,
    fontSize: 16 // 预览时使用固定字号
  });
};

const handleClose = () => {
  visible.value = false;
};

const handleSave = async () => {
  if (!formRef.value) return;

  try {
    // 验证表单
    await formRef.value.validate();

    // 验证配置
    const validation = ColorLabelUtils.validateConfig(props.config);
    if (!validation.isValid) {
      EleMessage.error(validation.errors[0]);
      return;
    }

    saving.value = true;

    // 准备保存数据
    const templateData = {
      ...props.config,
      customTemplateName: form.templateName,
      description: form.description,
      setAsDefault: form.setAsDefault,
      shareTemplate: form.shareTemplate,
      createTime: new Date().toISOString()
    };

    // 调用API保存模板
    await colorLabelApi.saveCustomTemplate(templateData);

    EleMessage.success('模板保存成功');
    emit('save-success', templateData);
    handleClose();

  } catch (error) {
    EleMessage.error('保存失败：' + error.message);
  } finally {
    saving.value = false;
  }
};
</script>

<style scoped>
.template-preview {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60px;
}

.preview-label {
  padding: 8px 16px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  text-align: center;
  min-width: 80px;
}

.config-summary {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #e4e7ed;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 4px 0;
}

.config-item:last-child {
  margin-bottom: 0;
}

.config-label {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.config-value {
  font-size: 13px;
  color: #303133;
  font-weight: 600;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表单样式调整 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #303133;
}

:deep(.el-checkbox) {
  margin-right: 20px;
  margin-bottom: 8px;
}

:deep(.el-checkbox:last-child) {
  margin-right: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .config-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .config-label {
    font-size: 12px;
  }
  
  .config-value {
    font-size: 12px;
  }
  
  .preview-label {
    padding: 6px 12px;
    min-width: 60px;
  }
}
</style>
