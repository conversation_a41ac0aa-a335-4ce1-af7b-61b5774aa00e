<template>
  <div class="template-selector">
    <!-- 预设模板 -->
    <div class="template-section">
      <div class="section-header">
        <h4>预设模板</h4>
        <el-button 
          size="small" 
          text 
          @click="refreshPresetTemplates"
          :loading="presetLoading"
        >
          <el-icon><Refresh /></el-icon>
        </el-button>
      </div>
      
      <div class="template-list">
        <div
          v-for="template in presetTemplates"
          :key="template.name"
          class="template-item"
          @click="selectTemplate(template)"
        >
          <div class="template-preview" v-html="template.preview"></div>
          <div class="template-info">
            <div class="template-name">{{ template.name }}</div>
            <div class="template-meta">
              <span class="template-color" :style="{ color: template.color }">
                {{ template.color }}
              </span>
              <span class="template-font">{{ template.fontFamily }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 自定义模板 -->
    <div class="template-section">
      <div class="section-header">
        <h4>自定义模板</h4>
        <el-button 
          size="small" 
          text 
          @click="refreshCustomTemplates"
          :loading="customLoading"
        >
          <el-icon><Refresh /></el-icon>
        </el-button>
      </div>
      
      <div class="template-list">
        <div
          v-for="template in customTemplates"
          :key="template.id"
          class="template-item custom-template"
          @click="selectTemplate(template)"
        >
          <div class="template-preview" v-html="template.preview"></div>
          <div class="template-info">
            <div class="template-name">{{ template.name }}</div>
            <div class="template-meta">
              <span class="template-color" :style="{ color: template.color }">
                {{ template.color }}
              </span>
              <span class="template-font">{{ template.fontFamily }}</span>
            </div>
          </div>
          <div class="template-actions">
            <el-button
              size="small"
              text
              type="danger"
              @click.stop="deleteTemplate(template)"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-if="!customLoading && customTemplates.length === 0" class="empty-custom">
          <el-empty description="暂无自定义模板" :image-size="40" />
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="presetLoading && presetTemplates.length === 0" class="loading-container">
      <el-skeleton :rows="2" animated />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { Refresh, Delete } from '@element-plus/icons-vue';
import { EleMessage } from 'ele-admin-plus/es';
import { ElMessageBox } from 'element-plus';
import { colorLabelApi, ColorLabelUtils } from '../api';

const emit = defineEmits(['template-select']);

// 响应式数据
const presetTemplates = ref([]);
const customTemplates = ref([]);
const presetLoading = ref(false);
const customLoading = ref(false);

// 生命周期
onMounted(async () => {
  await Promise.all([
    loadPresetTemplates(),
    loadCustomTemplates()
  ]);
});

// 方法
const loadPresetTemplates = async () => {
  try {
    presetLoading.value = true;
    const templates = await colorLabelApi.getPresetTemplates();
    presetTemplates.value = templates;
  } catch (error) {
    console.error('加载预设模板失败:', error);
    // 使用默认模板
    presetTemplates.value = getDefaultPresetTemplates();
  } finally {
    presetLoading.value = false;
  }
};

const loadCustomTemplates = async () => {
  try {
    customLoading.value = true;
    const templates = await colorLabelApi.getCustomTemplates();
    customTemplates.value = templates;
  } catch (error) {
    console.error('加载自定义模板失败:', error);
    customTemplates.value = [];
  } finally {
    customLoading.value = false;
  }
};

const refreshPresetTemplates = async () => {
  await loadPresetTemplates();
  EleMessage.success('预设模板已刷新');
};

const refreshCustomTemplates = async () => {
  await loadCustomTemplates();
  EleMessage.success('自定义模板已刷新');
};

const selectTemplate = (template) => {
  emit('template-select', template);
};

const deleteTemplate = async (template) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板"${template.name}"吗？`,
      '删除确认',
      {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    );

    await colorLabelApi.deleteCustomTemplate(template.id);
    await loadCustomTemplates();
    EleMessage.success('模板删除成功');
  } catch (error) {
    if (error !== 'cancel') {
      EleMessage.error('删除失败：' + error.message);
    }
  }
};

const getDefaultPresetTemplates = () => {
  const templates = ColorLabelUtils.getPresetTemplateData();
  return templates.map(template => ({
    name: template.name,
    color: template.config.textColor,
    fontFamily: template.config.fontFamily,
    content: template.config.labelText,
    config: template.config,
    preview: generateTemplatePreview(template.config)
  }));
};

const generateTemplatePreview = (config) => {
  return `
    <div style="
      color: ${config.textColor}; 
      font-family: ${config.fontFamily}; 
      font-size: 12px; 
      font-weight: ${config.fontWeight || 'normal'};
      padding: 4px 8px;
      border: 1px solid #e4e7ed;
      border-radius: 3px;
      background: #fafafa;
      text-align: center;
      min-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    ">
      ${config.labelText}
    </div>
  `;
};
</script>

<style scoped>
.template-selector {
  height: 100%;
}

.template-section {
  margin-bottom: 24px;
}

.template-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.template-list {
  max-height: 300px;
  overflow-y: auto;
  padding-right: 5px;
}

.template-item {
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  margin-bottom: 8px;
  position: relative;
}

.template-item:hover {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.template-item:active {
  transform: translateY(0);
}

.template-preview {
  width: 80px;
  margin-right: 12px;
  flex-shrink: 0;
}

.template-info {
  flex: 1;
  min-width: 0;
}

.template-name {
  font-weight: 600;
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.template-meta {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.template-color {
  font-size: 11px;
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.template-font {
  font-size: 11px;
  color: #909399;
}

.template-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.template-item:hover .template-actions {
  opacity: 1;
}

.custom-template {
  border-left: 3px solid #409eff;
}

.empty-custom {
  padding: 20px;
  text-align: center;
}

.loading-container {
  padding: 20px;
}

/* 滚动条样式 */
.template-list::-webkit-scrollbar {
  width: 6px;
}

.template-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.template-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.template-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .template-item {
    padding: 8px;
  }
  
  .template-preview {
    width: 60px;
    margin-right: 10px;
  }
  
  .template-name {
    font-size: 13px;
  }
  
  .template-color,
  .template-font {
    font-size: 10px;
  }
}
</style>
