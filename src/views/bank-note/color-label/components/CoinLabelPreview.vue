<template>
  <div class="coin-label-preview">
    <!-- 工具栏 -->
    <div class="preview-toolbar">
      <div class="toolbar-left">
        <span class="preview-title">钱币标签预览</span>
        <el-tag size="small" type="success">可视化定位</el-tag>
      </div>
      <div class="toolbar-right">
        <el-button-group size="small">
          <el-button @click="zoomOut" :disabled="zoomLevel <= 0.5">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
          <el-button @click="resetZoom">
            {{ Math.round(zoomLevel * 100) }}%
          </el-button>
          <el-button @click="zoomIn" :disabled="zoomLevel >= 2">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
        </el-button-group>
        <el-button size="small" @click="toggleGrid" :type="showGrid ? 'primary' : 'default'">
          <el-icon><Grid /></el-icon>
          网格
        </el-button>
        <el-button size="small" @click="resetPosition">
          <el-icon><RefreshLeft /></el-icon>
          重置位置
        </el-button>
      </div>
    </div>

    <!-- 预览容器 -->
    <div class="preview-container" :style="{ transform: `scale(${zoomLevel})` }">
      <!-- 网格背景 -->
      <div v-if="showGrid" class="grid-background"></div>
      
      <!-- 钱币标签模板 -->
      <div class="coin-label-template" ref="templateRef">
        <!-- 钱币信息区域 -->
        <div class="coin-info-section">
          <div class="coin-title">中国人民银行</div>
          <div class="coin-year">1980年 贰角</div>
          <div class="coin-serial">S/N FZ-07477901 - 民族人物头像</div>
        </div>

        <!-- 评分区域 -->
        <div class="score-section">
          <div class="score-number">68</div>
          <div class="score-text">
            <div>EPQ</div>
            <div>Superb Gem Unc</div>
          </div>
        </div>

        <!-- 二维码区域 -->
        <div class="qr-code-section">
          <div class="qr-code-placeholder"></div>
          <div class="qr-code-text">ZK25080001</div>
        </div>

        <!-- 彩色标签定位区域 -->
        <div 
          class="color-label-overlay"
          :class="{ 'dragging': isDragging }"
          :style="colorLabelStyle"
          @mousedown="startDrag"
          @click="selectColorLabel"
        >
          <div class="color-label-content" v-if="colorLabelConfig.labelText">
            {{ colorLabelConfig.labelText }}
          </div>
          <div class="color-label-placeholder" v-else>
            拖拽定位彩色标签
          </div>
          
          <!-- 调整手柄 -->
          <div class="resize-handles" v-if="isSelected">
            <div class="resize-handle resize-handle-nw" @mousedown.stop="startResize('nw')"></div>
            <div class="resize-handle resize-handle-ne" @mousedown.stop="startResize('ne')"></div>
            <div class="resize-handle resize-handle-sw" @mousedown.stop="startResize('sw')"></div>
            <div class="resize-handle resize-handle-se" @mousedown.stop="startResize('se')"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 位置调整面板 -->
    <div class="position-panel">
      <el-card header="精确定位">
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item label="X坐标">
              <el-input-number
                v-model="position.x"
                :min="0"
                :max="templateWidth"
                size="small"
                @change="updatePosition"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="Y坐标">
              <el-input-number
                v-model="position.y"
                :min="0"
                :max="templateHeight"
                size="small"
                @change="updatePosition"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="宽度">
              <el-input-number
                v-model="position.width"
                :min="20"
                :max="200"
                size="small"
                @change="updatePosition"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="高度">
              <el-input-number
                v-model="position.height"
                :min="10"
                :max="100"
                size="small"
                @change="updatePosition"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 快速定位按钮 -->
        <div class="quick-position-buttons">
          <el-button size="small" @click="setQuickPosition('center')">居中</el-button>
          <el-button size="small" @click="setQuickPosition('between-info-score')">信息与评分间</el-button>
          <el-button size="small" @click="setQuickPosition('below-serial')">序列号下方</el-button>
          <el-button size="small" @click="setQuickPosition('left-of-score')">评分左侧</el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { ZoomIn, ZoomOut, Grid, RefreshLeft } from '@element-plus/icons-vue';

const props = defineProps({
  colorLabelConfig: {
    type: Object,
    default: () => ({})
  },
  coinData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['position-change', 'label-select']);

// 响应式数据
const templateRef = ref(null);
const zoomLevel = ref(1);
const showGrid = ref(true);
const isDragging = ref(false);
const isSelected = ref(false);
const isResizing = ref(false);

// 模板尺寸（基于实际钱币标签尺寸）
const templateWidth = 200; // mm转换为像素
const templateHeight = 60;

// 彩色标签位置和尺寸
const position = reactive({
  x: 120, // 默认位置：钱币信息和评分之间
  y: 25,
  width: 60,
  height: 20
});

// 拖拽状态
const dragState = reactive({
  startX: 0,
  startY: 0,
  startPosX: 0,
  startPosY: 0,
  resizeType: null
});

// 计算属性
const colorLabelStyle = computed(() => ({
  position: 'absolute',
  left: `${position.x}px`,
  top: `${position.y}px`,
  width: `${position.width}px`,
  height: `${position.height}px`,
  backgroundColor: props.colorLabelConfig.backgroundColor || 'rgba(255, 255, 255, 0.9)',
  color: props.colorLabelConfig.textColor || '#000',
  fontFamily: props.colorLabelConfig.fontFamily || '宋体',
  fontSize: `${(props.colorLabelConfig.fontSize || 14) * 0.8}px`,
  fontWeight: props.colorLabelConfig.fontWeight || 'normal',
  border: isSelected.value ? '2px solid #409eff' : '1px dashed #ccc',
  borderRadius: '2px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  cursor: isDragging.value ? 'grabbing' : 'grab',
  userSelect: 'none',
  zIndex: 10
}));

// 生命周期
onMounted(() => {
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
});

onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
});

// 方法
const zoomIn = () => {
  if (zoomLevel.value < 2) {
    zoomLevel.value = Math.min(zoomLevel.value + 0.1, 2);
  }
};

const zoomOut = () => {
  if (zoomLevel.value > 0.5) {
    zoomLevel.value = Math.max(zoomLevel.value - 0.1, 0.5);
  }
};

const resetZoom = () => {
  zoomLevel.value = 1;
};

const toggleGrid = () => {
  showGrid.value = !showGrid.value;
};

const resetPosition = () => {
  Object.assign(position, {
    x: 120,
    y: 25,
    width: 60,
    height: 20
  });
  updatePosition();
};

const startDrag = (event) => {
  if (isResizing.value) return;
  
  isDragging.value = true;
  isSelected.value = true;
  
  dragState.startX = event.clientX;
  dragState.startY = event.clientY;
  dragState.startPosX = position.x;
  dragState.startPosY = position.y;
  
  event.preventDefault();
};

const startResize = (type) => {
  isResizing.value = true;
  dragState.resizeType = type;
  
  event.preventDefault();
};

const handleMouseMove = (event) => {
  if (isDragging.value && !isResizing.value) {
    const deltaX = (event.clientX - dragState.startX) / zoomLevel.value;
    const deltaY = (event.clientY - dragState.startY) / zoomLevel.value;
    
    position.x = Math.max(0, Math.min(templateWidth - position.width, dragState.startPosX + deltaX));
    position.y = Math.max(0, Math.min(templateHeight - position.height, dragState.startPosY + deltaY));
    
    updatePosition();
  } else if (isResizing.value) {
    handleResize(event);
  }
};

const handleMouseUp = () => {
  isDragging.value = false;
  isResizing.value = false;
  dragState.resizeType = null;
};

const handleResize = (event) => {
  const deltaX = (event.clientX - dragState.startX) / zoomLevel.value;
  const deltaY = (event.clientY - dragState.startY) / zoomLevel.value;
  
  switch (dragState.resizeType) {
    case 'se': // 右下角
      position.width = Math.max(20, Math.min(200, position.width + deltaX));
      position.height = Math.max(10, Math.min(100, position.height + deltaY));
      break;
    case 'sw': // 左下角
      position.width = Math.max(20, position.width - deltaX);
      position.x = Math.max(0, position.x + deltaX);
      position.height = Math.max(10, position.height + deltaY);
      break;
    case 'ne': // 右上角
      position.width = Math.max(20, position.width + deltaX);
      position.height = Math.max(10, position.height - deltaY);
      position.y = Math.max(0, position.y + deltaY);
      break;
    case 'nw': // 左上角
      position.width = Math.max(20, position.width - deltaX);
      position.height = Math.max(10, position.height - deltaY);
      position.x = Math.max(0, position.x + deltaX);
      position.y = Math.max(0, position.y + deltaY);
      break;
  }
  
  updatePosition();
};

const selectColorLabel = () => {
  isSelected.value = true;
  emit('label-select');
};

const updatePosition = () => {
  emit('position-change', { ...position });
};

const setQuickPosition = (type) => {
  switch (type) {
    case 'center':
      position.x = (templateWidth - position.width) / 2;
      position.y = (templateHeight - position.height) / 2;
      break;
    case 'between-info-score':
      position.x = 120;
      position.y = 25;
      break;
    case 'below-serial':
      position.x = 20;
      position.y = 45;
      break;
    case 'left-of-score':
      position.x = 90;
      position.y = 15;
      break;
  }
  updatePosition();
};

// 暴露方法给父组件
defineExpose({
  getPosition: () => ({ ...position }),
  setPosition: (newPosition) => Object.assign(position, newPosition),
  resetPosition,
  zoomIn,
  zoomOut,
  resetZoom
});
</script>

<style scoped>
.coin-label-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafbfc;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.preview-title {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-container {
  flex: 1;
  padding: 20px;
  background: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: auto;
  transform-origin: center;
  transition: transform 0.2s ease;
}

.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(to right, #e0e0e0 1px, transparent 1px),
    linear-gradient(to bottom, #e0e0e0 1px, transparent 1px);
  background-size: 10px 10px;
  opacity: 0.3;
  pointer-events: none;
}

.coin-label-template {
  width: 200px;
  height: 60px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  display: flex;
  padding: 8px;
  font-size: 10px;
  line-height: 1.2;
}

.coin-info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.coin-title {
  font-weight: bold;
  font-size: 11px;
}

.coin-year {
  font-size: 10px;
  margin: 2px 0;
}

.coin-serial {
  font-size: 9px;
  color: #666;
}

.score-section {
  display: flex;
  align-items: center;
  margin: 0 10px;
}

.score-number {
  font-size: 24px;
  font-weight: bold;
  margin-right: 8px;
}

.score-text {
  font-size: 8px;
  text-align: center;
}

.qr-code-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.qr-code-placeholder {
  width: 30px;
  height: 30px;
  background: #f0f0f0;
  border: 1px solid #ccc;
  margin-bottom: 2px;
}

.qr-code-text {
  font-size: 7px;
  text-align: center;
}

.color-label-overlay {
  transition: all 0.2s ease;
}

.color-label-overlay.dragging {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.color-label-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  word-break: break-all;
}

.color-label-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 10px;
  text-align: center;
}

.resize-handles {
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  width: 6px;
  height: 6px;
  background: #409eff;
  border: 1px solid white;
  border-radius: 50%;
  pointer-events: all;
  cursor: pointer;
}

.resize-handle-nw {
  top: 0;
  left: 0;
  cursor: nw-resize;
}

.resize-handle-ne {
  top: 0;
  right: 0;
  cursor: ne-resize;
}

.resize-handle-sw {
  bottom: 0;
  left: 0;
  cursor: sw-resize;
}

.resize-handle-se {
  bottom: 0;
  right: 0;
  cursor: se-resize;
}

.position-panel {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
  background: white;
}

.quick-position-buttons {
  margin-top: 12px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.quick-position-buttons .el-button {
  margin: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .preview-container {
    padding: 10px;
  }
  
  .coin-label-template {
    transform: scale(0.8);
  }
  
  .position-panel .el-row {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
