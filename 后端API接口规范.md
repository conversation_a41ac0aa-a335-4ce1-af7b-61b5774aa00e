# 彩色标签打印功能 - 后端API接口规范

## 📋 概述

为了支持新的一体化彩色标签打印功能，需要在后端实现以下新的API接口。这些接口将支持钱币数据获取、位置预览、一体化打印等功能。

## 🔗 基础信息

- **基础路径**: `/api/banknote/color-label`
- **认证方式**: <PERSON>er <PERSON>
- **响应格式**: JSON
- **字符编码**: UTF-8

## 📡 新增API接口

### 1. 获取钱币数据

#### 1.1 获取单个钱币数据
```http
GET /api/banknote/color-label/coin/{coinId}
```

**请求参数**:
- `coinId` (路径参数): 钱币ID

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "1",
    "serialNumber": "FZ-********",
    "bankName": "中国人民银行",
    "year": "1980年",
    "denomination": "贰角",
    "description": "民族人物头像",
    "score": "68",
    "gradeType": "EPQ",
    "gradeDescription": "Superb Gem Unc",
    "qrCode": "ZK25080001"
  }
}
```

#### 1.2 批量获取钱币数据
```http
POST /api/banknote/color-label/coins/batch
```

**请求体**:
```json
{
  "coinIds": ["1", "2", "3"]
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "1",
      "serialNumber": "FZ-********",
      // ... 其他字段
    }
  ]
}
```

### 2. 位置预览功能

#### 2.1 生成带位置信息的预览
```http
POST /api/banknote/color-label/position-preview
```

**请求体**:
```json
{
  "labelText": "18k纸黄金",
  "textColor": "#FFD700",
  "fontFamily": "宋体",
  "fontSize": 32,
  "fontWeight": "bold",
  "position": {
    "x": 120,
    "y": 25,
    "width": 60,
    "height": 20
  },
  "coinData": {
    "id": "1",
    "serialNumber": "FZ-********",
    // ... 钱币数据
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "previewHtml": "<div class='coin-label'>...</div>",
    "previewCss": ".coin-label { ... }",
    "positionValid": true,
    "warnings": [],
    "suggestions": ["当前位置在推荐区域：信息与评分间"]
  }
}
```

#### 2.2 验证标签位置
```http
POST /api/banknote/color-label/validate-position
```

**请求体**:
```json
{
  "position": {
    "x": 120,
    "y": 25,
    "width": 60,
    "height": 20
  },
  "templateSize": {
    "width": 200,
    "height": 60
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "isValid": true,
    "warnings": [],
    "suggestions": ["当前位置在推荐区域"],
    "inRecommendedArea": true,
    "recommendedPositions": [
      {
        "name": "信息与评分间",
        "x": 120,
        "y": 25,
        "width": 60,
        "height": 20
      }
    ]
  }
}
```

### 3. 一体化打印功能

#### 3.1 生成一体化打印数据
```http
POST /api/banknote/color-label/integrated-print
```

**请求体**:
```json
{
  "labelConfig": {
    "labelText": "18k纸黄金",
    "textColor": "#FFD700",
    "fontFamily": "宋体",
    "fontSize": 32,
    "fontWeight": "bold",
    "backgroundColor": "",
    "enableShadow": false,
    "enableBorder": false
  },
  "position": {
    "x": 120,
    "y": 25,
    "width": 60,
    "height": 20
  },
  "coinDataList": [
    {
      "id": "1",
      "serialNumber": "FZ-********",
      // ... 钱币数据
    }
  ]
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "html": "<!DOCTYPE html><html>...</html>",
    "css": "@page { size: A4; margin: 10mm; } ...",
    "totalLabels": 10,
    "pageCount": 1,
    "printSettings": {
      "pageSize": "A4",
      "orientation": "portrait",
      "margin": "10mm"
    }
  }
}
```

### 4. 位置配置管理

#### 4.1 保存位置配置
```http
POST /api/banknote/color-label/position-config
```

**请求体**:
```json
{
  "templateName": "18k纸黄金模板",
  "position": {
    "x": 120,
    "y": 25,
    "width": 60,
    "height": 20
  },
  "labelConfig": {
    "labelText": "18k纸黄金",
    "textColor": "#FFD700",
    // ... 其他配置
  },
  "isDefault": false
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "config_123",
    "templateName": "18k纸黄金模板",
    "createTime": "2025-08-03T10:30:00Z"
  }
}
```

#### 4.2 获取位置配置
```http
GET /api/banknote/color-label/position-config/{templateId}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "config_123",
    "templateName": "18k纸黄金模板",
    "position": {
      "x": 120,
      "y": 25,
      "width": 60,
      "height": 20
    },
    "labelConfig": {
      // ... 标签配置
    },
    "createTime": "2025-08-03T10:30:00Z",
    "updateTime": "2025-08-03T10:30:00Z"
  }
}
```

### 5. 钱币标签模板

#### 5.1 获取钱币标签模板列表
```http
GET /api/banknote/color-label/coin-templates
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "template_1",
      "name": "标准钱币标签",
      "width": 200,
      "height": 60,
      "description": "适用于大部分钱币的标准标签模板",
      "recommendedAreas": [
        {
          "name": "信息与评分间",
          "x": 100,
          "y": 20,
          "width": 40,
          "height": 20
        }
      ]
    }
  ]
}
```

## 🔧 实现要点

### 1. 数据库设计

#### 位置配置表 (color_label_position_config)
```sql
CREATE TABLE color_label_position_config (
  id VARCHAR(50) PRIMARY KEY,
  template_name VARCHAR(100) NOT NULL,
  position_x INT NOT NULL,
  position_y INT NOT NULL,
  position_width INT NOT NULL,
  position_height INT NOT NULL,
  label_config TEXT, -- JSON格式的标签配置
  is_default BOOLEAN DEFAULT FALSE,
  user_id VARCHAR(50),
  create_time DATETIME,
  update_time DATETIME
);
```

#### 钱币标签模板表 (coin_label_template)
```sql
CREATE TABLE coin_label_template (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  width INT NOT NULL,
  height INT NOT NULL,
  description TEXT,
  template_html TEXT,
  template_css TEXT,
  recommended_areas TEXT, -- JSON格式的推荐区域
  create_time DATETIME,
  update_time DATETIME
);
```

### 2. 核心算法

#### 2.1 位置验证算法
```java
public class PositionValidator {
    public ValidationResult validatePosition(Position position, TemplateSize templateSize) {
        List<String> warnings = new ArrayList<>();
        List<String> suggestions = new ArrayList<>();
        
        // 边界检查
        if (position.getX() + position.getWidth() > templateSize.getWidth()) {
            warnings.add("标签超出右边界");
        }
        
        // 推荐区域检查
        boolean inRecommendedArea = checkRecommendedAreas(position);
        
        return new ValidationResult(warnings.isEmpty(), warnings, suggestions, inRecommendedArea);
    }
}
```

#### 2.2 HTML生成算法
```java
public class IntegratedPrintGenerator {
    public PrintResult generateIntegratedPrint(PrintConfig config) {
        StringBuilder html = new StringBuilder();
        
        // 生成页面头部
        html.append(generateHtmlHeader(config));
        
        // 生成钱币标签
        for (CoinData coin : config.getCoinDataList()) {
            html.append(generateCoinLabel(coin, config.getLabelConfig(), config.getPosition()));
        }
        
        // 生成页面尾部
        html.append(generateHtmlFooter());
        
        return new PrintResult(html.toString(), generateCSS(config), calculatePageCount(config));
    }
}
```

### 3. 性能优化

1. **缓存策略**: 对钱币数据和模板进行缓存
2. **批量处理**: 支持批量获取钱币数据
3. **异步生成**: 大量标签生成时使用异步处理
4. **分页处理**: 超过一定数量的标签自动分页

### 4. 错误处理

1. **参数验证**: 严格验证所有输入参数
2. **业务异常**: 定义明确的业务异常类型
3. **降级策略**: 在服务不可用时提供基础功能
4. **日志记录**: 详细记录操作日志和错误信息

## 🧪 测试用例

### 1. 单元测试
- 位置验证算法测试
- HTML生成算法测试
- 数据转换测试

### 2. 集成测试
- API接口测试
- 数据库操作测试
- 缓存功能测试

### 3. 性能测试
- 大量数据处理测试
- 并发访问测试
- 内存使用测试

## 📝 部署说明

1. **数据库迁移**: 执行数据库脚本创建新表
2. **配置更新**: 更新应用配置文件
3. **权限配置**: 添加新的权限点
4. **监控配置**: 配置新接口的监控和告警

## 🔄 版本兼容

- 保持现有API接口不变
- 新增接口使用新的路径
- 提供数据迁移工具
- 支持渐进式升级
